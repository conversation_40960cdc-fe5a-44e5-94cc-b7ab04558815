import React from 'react'
import PropTypes from 'prop-types'
import clsx from 'clsx'
import Select from 'react-select'
import Async, { makeAsyncSelect } from 'react-select/async'
import AsyncSelect from 'react-select/async'
import CreatableSelect from 'react-select/creatable'

import { emphasize, makeStyles, useTheme } from '@material-ui/core/styles'
import { Typography, NoSsr, TextField, Paper, Chip, MenuItem } from '@material-ui/core'

import CancelIcon from '@material-ui/icons/Cancel'
// lang strings
import { langMessages } from '../../../lang'

const useStyles = makeStyles(theme => ({
  root: {
    flexGrow: 1,
    minWidth: 200,
  },
  input: {
    display: 'flex',
    height: 'auto',
    minHeight: 56,
    padding: '8px 0 8px 10px',
    boxSizing: 'border-box',
  },
  valueContainer: {
    display: 'flex',
    flexWrap: 'wrap',
    flex: 1,
    alignItems: 'center',
    overflow: 'hidden',
  },
  chip: {
    margin: theme.spacing(0.5, 0.25),
  },
  chipFocused: {
    backgroundColor: emphasize(theme.palette.type === 'light' ? theme.palette.grey[300] : theme.palette.grey[700], 0.08),
  },
  noOptionsMessage: {
    padding: theme.spacing(1, 2),
  },
  singleValue: {
    fontSize: 16,
  },
  placeholder: {
    left: 15,
    bottom: '50%',
    position: 'absolute',
    fontSize: 16,
    transform: 'translate(0, 50%)',
  },
  paper: {
    position: 'absolute',
    zIndex: 99,
    marginTop: theme.spacing(1),
    left: 0,
    right: 0,
  },
  divider: {
    height: theme.spacing(2),
  },
}))

function NoOptionsMessage(props) {
  return (
    <Typography color="textSecondary" className={`ReactSelect-NoOptionsMessage ${props.selectProps.classes.noOptionsMessage}`} {...props.innerProps}>
      {props.children}
    </Typography>
  )
}

function inputComponent({ inputRef, ...props }) {
  return <div ref={inputRef} {...props} />
}

function Control(props) {
  const {
    children,
    innerProps,
    innerRef,
    selectProps: { classes, TextFieldProps, InputProps },
  } = props

  return (
    <TextField
      fullWidth
      {...TextFieldProps}
      InputProps={{
        inputComponent,
        inputProps: {
          className: classes.input,
          ref: innerRef,
          children,
          ...innerProps,
        },
        ...InputProps,
        ...TextFieldProps.InputProps,
      }}
    />
  )
}

function Option(props) {
  return (
    <MenuItem
      ref={props.innerRef}
      selected={props.isFocused}
      component="div"
      style={{
        fontWeight: props.isSelected ? 500 : 400,
      }}
      {...props.innerProps}
    >
      {props.children}
    </MenuItem>
  )
}

function Placeholder(props) {
  const { selectProps, innerProps = {}, children } = props
  return (
    <Typography color="textSecondary" className={`ReactSelect-Placeholder ${selectProps.classes.placeholder}`} {...innerProps}>
      {children}
    </Typography>
  )
}

function SingleValue(props) {
  return (
    <Chip
      tabIndex={-1}
      label={props.children}
      className={props.selectProps.classes.singleValue}
      onDelete={props.removeProps.onClick}
      deleteIcon={<CancelIcon {...props.removeProps} />}
      {...props.innerProps}
    >
      {props.children}
    </Chip>
  )
}

function ValueContainer(props) {
  return <div className={props.selectProps.classes.valueContainer}>{props.children}</div>
}

function MultiValue(props) {
  return (
    <Chip
      tabIndex={-1}
      label={props.children}
      className={clsx(props.selectProps.classes.chip, {
        [props.selectProps.classes.chipFocused]: props.isFocused,
      })}
      onDelete={props.removeProps.onClick}
      deleteIcon={<CancelIcon {...props.removeProps} />}
    />
  )
}

function Menu(props) {
  return (
    <Paper square className={props.selectProps.classes.paper} {...props.innerProps}>
      {props.children}
    </Paper>
  )
}

const components = {
  Control,
  Menu,
  MultiValue,
  NoOptionsMessage,
  Option,
  Placeholder,
  SingleValue,
  ValueContainer,
}

// console.log('selectedItems',selectedItems);
const promiseOptions = inputValue =>
  new Promise(resolve => {
    setTimeout(() => {
      resolve(filterColors(inputValue))
    }, 1000)
  })

const IntegrationReactSelect = props => {
  const classes = useStyles()
  const theme = useTheme()

  const {
    onChange,
    handleCreate,
    inputValueHandler,
    label,
    isMulti,
    isClearable,
    placeholder,
    suggestions,
    selectedItems,
    noItemsText,
    loadingMessage,
    createMessage,
    TextFieldProps,
    customComponents,
    InputProps,
  } = props

  const isAsync = !!props.promiseOptions
  const isCreatable = !!props.handleCreate

  const handleChange = value => {
    if (!isMulti) {
      value = value && value.length ? [value[value.length - 1]] : []
    }
    onChange(value)
    // console.log('handleChange > value',value);
  }

  const handleInputChange = inputValue => {
    let newValue = inputValue
    inputValueHandler && (newValue = inputValueHandler(inputValue))
    // console.log('handleInputChange > inputValue',inputValue);
    // console.log('handleInputChange > newValue',newValue);
    return newValue
  }

  const selectStyles = {
    input: base => ({
      ...base,
      color: theme.palette.text.primary,
      '& input': {
        font: 'inherit',
      },
    }),
  }

  const randId = new Date().getTime().toString()

  const SelectProps = {
    classes,
    styles: selectStyles,
    inputId: `react-select-multiple-${randId}`,
    InputProps,
    TextFieldProps: {
      label: label || null,
      InputLabelProps: {
        htmlFor: `react-select-multiple-${randId}`,
        shrink: true,
      },
      ...(TextFieldProps || {}),
    },
    placeholder: placeholder || langMessages['placeholders.select'],
    isClearable,
    components: { ...components, ...(customComponents || {}) },
    value: selectedItems,
    onChange: handleChange,
    onInputChange: handleInputChange,
    noOptionsMessage: m => (noItemsText || langMessages['texts.noItemsAvailable']).replace('[%s]', m.inputValue),
    loadingMessage: m => (loadingMessage || langMessages['texts.loading']).replace('[%s]', (m && m.inputValue) || ''),
    formatCreateLabel: inputValue =>
      'createMessage' in props ? `${createMessage} ${inputValue}` : langMessages['placeholders.create'].replace('[%s]', inputValue || ''),
  }

  return (
    <div className={classes.root}>
      <NoSsr>
        {isCreatable ? (
          <CreatableSelect
            isMulti
            // isLoading={isLoading}
            onCreateOption={handleCreate}
            options={suggestions}
            {...SelectProps}
          />
        ) : isAsync ? (
          <AsyncSelect isMulti cacheOptions defaultOptions {...SelectProps} loadOptions={props.promiseOptions} />
        ) : (
          <Select isMulti {...SelectProps} options={suggestions} />
        )}
      </NoSsr>
    </div>
  )
}

export default IntegrationReactSelect
