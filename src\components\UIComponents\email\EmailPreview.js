import React from 'react'

// lang strings
import { langMessages } from '../../../lang'

import EmailHead from 'UIComponents/email/EmailHead'

import { Modal, ModalBody, ModalFooter } from 'reactstrap'

import { Button } from '@material-ui/core'

class EmailPreview extends React.Component {
  render() {
    const {
      email,
      email: { content },
      isOpen,
      onDismiss,
      grapesJs
    } = this.props


    return (
      <Modal fade={true} className={'xl-modal'} isOpen={isOpen !== false} toggle={() => onDismiss()} backdrop={'static'}>
        <ModalBody>
          <div className="email-preview email-preview-wrapper" style={{ width: '100%', minHeight: 300, maxWidth: '90vw' }}>
            <EmailHead {...email.settings} />
            <div className="content-wrapper">
              {grapesJs ? (
                <iframe width="100%" height="360" srcDoc={content || ''}> </iframe>
              ) : (
                <div className="preview" ref={div => div && (div.innerHTML = content)} style={{ maxWidth: 640, margin: '0 auto' }}>
                  {content || ''}
                </div>
              )}
            </div>
          </div>
        </ModalBody>
        <ModalFooter>
          <Button onClick={() => onDismiss()} className="btn-primary text-white" autoFocus>
            {langMessages['button.accept']}
          </Button>
        </ModalFooter>
      </Modal>
    )
  }
}

export default EmailPreview
