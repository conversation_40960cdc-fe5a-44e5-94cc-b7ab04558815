/**
 * Help Tooltip
 */
import React from 'react'

// lang strings
import { langMessages } from '../../../lang'

import { Badge } from 'reactstrap'
import { Tooltip } from '@material-ui/core'

const HelpTooltip = ({ title, contents, placement, ...props }) => (
  <Tooltip
    placement={placement || 'top'}
    classes={{ tooltip: 'bg-white text-dark p-10 radius-10 qiHelpTooltip' }}
    title={
      <div className="qiHelpTooltip-content">
        {!!title && <div className="qiHelpTooltip-title">{title}</div>}
        <div className="qiHelpTooltip-body">{contents}</div>
      </div>
    }
  >
    <div className="qi-help d-inline-block">
      <Badge pill color="dark">
        ?
      </Badge>
    </div>
  </Tooltip>
)

export default HelpTooltip
