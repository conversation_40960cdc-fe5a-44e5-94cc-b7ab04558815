/**
 * Linear Interminate ProgressBar
 */
import React from 'react'

function StepperButtonGroup({ steps, currentStep, navigate, onClick, ...props }) {
  return (
    <div className={`StepperButtonGroupComponent StepperButtonGroupComponent-wrapper ${props.light ? 'light' : ''} ${props.wrapperClasses || ''}`}>
      <div className="steps-wrapper d-flex align-items-center">
        {!!navigate && (
          <div className={`prev step-nav ${currentStep > 1 ? 'active' : ''}`} onClick={e => (currentStep > 1 ? navigate(currentStep - 1) : () => {})}>
            <i className="ti-angle-left" />
          </div>
        )}
        <ul className="steps">
          {steps.map((step, k) => {
            return (
              <li
                onClick={e => !!onClick && onClick(k + 1)}
                className={`step ${currentStep === k + 1 ? 'active' : ''} ${currentStep > k + 1 ? 'past' : ''}`}
                key={k}
              >
                <div>
                  <span className="label">{step.label}</span>
                </div>
              </li>
            )
          })}
        </ul>
        {!!navigate && (
          <div
            className={`next step-nav ${currentStep < steps.length ? 'active' : ''}`}
            onClick={e => (currentStep < steps.length ? navigate(currentStep + 1) : () => {})}
          >
            <i className="ti-angle-right" />
          </div>
        )}
      </div>
    </div>
  )
}

export default StepperButtonGroup
