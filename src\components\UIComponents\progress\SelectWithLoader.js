/**
 * Linear Interminate ProgressBar
 */
import React from 'react'

// rct section loader
import RctSectionLoader from 'Components/RctSectionLoader/RctSectionLoader'

function SelectWithLoader({ children, fetching, classes, size, pos, ...props }) {
  classes = classes || {}
  return (
    <div className={`SelectPlaceholderComponent SelectPlaceholderComponent-wrapper SelectWithLoader size-${size || 'normal'} pos-${pos || 'end'}`}>
      {fetching && (
        <div className={`SelectPlaceholderComponent-loader ${classes.loaderWrapper || ''}`}>
          <RctSectionLoader className={classes.loader || ''} size={size || ''} pos={pos || 'end'} />
        </div>
      )}
      <div className="SelectPlaceholderComponent-children">{children}</div>
    </div>
  )
}

export default SelectWithLoader
