import React from 'react'


// lang strings
import { langMessages } from '../../../lang'

import SweetAlert from 'react-bootstrap-sweetalert'
import { NotificationManager } from 'react-notifications'

import ReactSelect from 'UIComponents/auto-complete/ReactSelect'
import countries from '../../../assets/data/countries'

import {
  Button,
  ButtonGroup,
  Checkbox,
  FormControlLabel,
  FormGroup,
  FormLabel,
  IconButton,
  InputAdornment,
  Radio,
  RadioGroup,
  Switch,
  TextField,
  Tooltip
} from '@material-ui/core'

import { DatePicker } from '@material-ui/pickers'
import { MOMENT_ISO, MOMENT_SHORT } from 'Constants'
import { clipboardCopy } from 'Helpers/helpers'
import { BlockPicker, ChromePicker, GithubPicker } from 'react-color'
import { CurrencyInput, TextMaskInput } from '../../../helpers/formHelpers'

import DTPicker from 'Widgets/DateTimePicker'
import PostsSelector from 'Widgets/PostsSelector'
import SimpleEditor from 'Widgets/SimpleEditor'
import Taxonomy from 'Widgets/Taxonomy'
import UsersDualListBox from 'Widgets/UsersDualListBox'
import UsersSelector from 'Widgets/UsersSelector'
import VideoPlayer from '../video-player/react-player'

import { MenuItem } from '@material-ui/core'
import LabeledDivider from '../dividers/LabeledDivider'
import DualListBox from '../duallistbox/DualListBox'
import ListBox from '../listbox/ListBox'
import CustomSlider from '../slider/CustomSlider'
import CountryInput from './CountryInput'
import PhoneInput from './PhoneInput'
import SelectInput from './SelectInput'

const randomId = () => Math.floor(Math.random() * (999999999999 - 111111111111 + 1)) + 111111111111

const newId = randomId()

let onChangeTimeout

const FlexInput = ({ field, value, onChange, ...props }) => {
  let FlexComponent = null

  const type = props.type || field.type
  const name = props.name || field.name

  if (!type) return null
  if (!field) field = props || {}

  // value = value || '';

  let { queries, choices, append, prepend, wrapper, dataType } = field

  wrapper = wrapper || {}

  let data = field.data || props.data || {}
  let numonly = field.numonly || props.numonly || false
  let readonly = field.readonly || props.readonly || false
  let required = field.required || props.required || false
  let clipboard = field.clipboard || props.clipboard || false
  let label = field.label || props.label || undefined
  let maxLength = field.maxlength || props.maxLength || undefined
  let placeholder = field.placeholder || props.placeholder || undefined
  let inputClasses = wrapper.inputClass || ''

  const [fieldVisibility, toggleVisibility] = React.useState(false)

  const [initialValue, setInitialValue] = React.useState(value)
  const [currentValue, updateValue] = React.useState(value)
  React.useEffect(() => updateValue(value), [value])
  let inputValue = currentValue

  const inputProps = {
    ...(field.inputProps || {}),
    ...(field.InputProps || {}),
    ...(props.inputProps || {}),
    ...(props.InputProps || {}),
    required: !!required || !!(props.InputProps || {}).required,
    readOnly: !!readonly || !!(props.InputProps || {}).readOnly,
    placeholder,
    label,
  }

  const InputLabelProps = {
    ...(props.InputLabelProps || {}),
    placeholder,
    label,
    shrink: 'shrink' in (props.InputLabelProps || {}) ? props.InputLabelProps.shrink : inputValue !== '',
  }

  if (!label) {
    delete inputProps.label
    delete InputLabelProps.label
  }

  if (props.InputLabelProps) {
    delete props.InputLabelProps
  }

  const collection = props.collection || field.collection
  const postId = props.postId || ''

  const notInputProps = ['searchFormatter', 'collection', 'postId']

  notInputProps.forEach(p => {
    if (p in inputProps) delete inputProps[p]
    if (p in props) delete props[p]
  })

  const { onFocus } = { ...field, ...props }
  const onInputFocus = ({ val, type, e }) => {
    setInitialValue(val)
    onFocus && onFocus(e)
  }

  const { onBlur, onValueChanged } = { ...field, ...props }
  const onInputBlur = ({ val, type, e }) => {
    !!onValueChanged && val !== initialValue && onValueChanged(val)
    setInitialValue(val)
    onBlur && onBlur(e)
  }

  if (onValueChanged) {
    if (props.onValueChanged) delete props.onValueChanged
    if (field.onValueChanged) delete field.onValueChanged
  }

  const onChangeInputValue = (val, type) => {
    const { formatter } = { ...field, ...props }

    if (numonly && val && typeof val === 'string') val = val.replace(/\D/g, '')
    if (formatter) val = formatter(val)
    if (maxLength && ['string', 'number', 'bigint'].includes(typeof val) && `${val}`.length > maxLength) {
      switch (typeof val) {
        case 'number':
          val = Number(`${val}`.slice(0, maxLength))
          break
        case 'bigint':
          val = BigInt(`${val}`.slice(0, maxLength))
          break
        case 'string':
          val = val.slice(0, maxLength)
          break
        default:
          break
      }
    }

    updateValue(val)

    switch (type) {
      case 'url':
      case 'textarea':
      case 'email':
      case 'text':
      case 'password':
      case 'percent':
      case 'number':
      case 'currency':
      case 'editor':
      case 'wysiwyg':
      case 'SimpleEditor':
        clearTimeout(onChangeTimeout)
        onChangeTimeout = setTimeout(() => onChange(val), 100)
        break
      default:
        onChange(val)
        break
    }
  }

  const adornments = {}

  if (append) {
    adornments.endAdornment = <InputAdornment position="end">{append}</InputAdornment>
  } else if (clipboard) {
    adornments.endAdornment = (
      <InputAdornment position="end">
        <Tooltip placement="top" title={langMessages['texts.copyToClipboard']}>
          <IconButton
            onClick={e => {
              e.preventDefault()
              let copyValue = currentValue
              if (type === 'script') {
                try {
                  copyValue = decodeURIComponent(currentValue)
                } catch (error) {
                  copyValue = currentValue
                }
              }
              clipboardCopy(copyValue, () => {
                NotificationManager.success(langMessages['texts.copiedToClipboard'])
              })
            }}
            size="small"
          >
            <i className="fa fa-clone" />
          </IconButton>
        </Tooltip>
      </InputAdornment>
    )
  }

  if (prepend) {
    adornments.startAdornment = <InputAdornment position="start">{prepend}</InputAdornment>
  }

  const [openPicker, togglePicker] = React.useState(false)
  const [alertMsg, setAlert] = React.useState('')

  let defaultValue = props.defaultValue || field.default_value || ''
  let isMulti

  switch (type) {
    case 'divider':
      FlexComponent = label && <LabeledDivider label={label} {...field} {...props} />
      break
    case 'validation':
    case 'hidden':
      let inputVal = value || field.value || defaultValue
      FlexComponent = <input type="hidden" name={name} value={value} ref={el => el && value !== inputVal && onChangeInputValue(inputVal, type)} />
      break
    case 'url':
    case 'textarea':
    case 'email':
    case 'text':
    case 'password':
      let fieldType = type
      let InputProps = { ...adornments, ...inputProps }

      switch (name) {
        case 'phone':
        case 'mobile':
        case 'cpf':
        case 'CPF':
        case 'postalCode':
        case 'cep':
        case 'CEP':
        case 'cnpj':
        case 'CNPJ':
          InputProps.inputComponent = TextMaskInput
          break
        case 'password':
          InputProps.endAdornment = (
            <InputAdornment position="end">
              <IconButton size="small" onClick={e => toggleVisibility(!fieldVisibility)}>
                <i className={`fa fa-eye${fieldVisibility ? '' : '-slash'}`} />
              </IconButton>
            </InputAdornment>
          )
          fieldType = fieldVisibility ? 'text' : 'password'
          break
        default:
          break
      }

      let rows = type === 'textarea' ? field.rows || props.rows || 5 : null

      inputValue = typeof field.min === 'number' ? Math.max(field.min, inputValue) : inputValue
      inputValue = typeof field.max === 'number' ? Math.min(field.max, inputValue) : inputValue

      FlexComponent = (
        <TextField
          variant="outlined"
          fullWidth
          type={fieldType}
          rows={rows}
          multiline={!!rows}
          className={`${props.className || ''} ${inputClasses}`}
          {...props}
          {...inputProps}
          InputProps={InputProps}
          InputLabelProps={InputLabelProps}
          maxLength={maxLength}
          name={name}
          value={inputValue}
          onChange={e => onChangeInputValue(e.target.value, fieldType)}
          onFocus={e => onInputFocus({ e, val: inputValue, type: fieldType })}
          onBlur={e => onInputBlur({ e, val: inputValue, type: fieldType })}
        />
      )
      break

    case 'script':
      FlexComponent = (
        <TextField
          variant="outlined"
          readOnly
          fullWidth
          multiline
          rows={field.rows || props.rows || 6}
          className={`${props.className || ''} ${inputClasses}`}
          type="text"
          {...props}
          {...inputProps}
          InputProps={{ ...adornments, ...inputProps }}
          InputLabelProps={InputLabelProps}
          onFocus={e => e.target.select()}
          onClick={e => e.target.select()}
          value={(inputValue && decodeURIComponent(inputValue)) || ''}
          onChange={e => {
            let v = encodeURIComponent(e.target.value)
            onChangeInputValue(v, fieldType)
          }}
        />
      )
      break

    case 'country':
      let returnKey = dataType === 'name' ? 'name' : 'countryCode'
      let countryData = currentValue && countries.find(c => (c[returnKey] || '').toLowerCase() === (currentValue || '').toLowerCase())

      const countryAdornments = (
        <CountryInput
          value={currentValue}
          onChange={countryData => {
            let updateVal = countryData[returnKey]
            onChangeInputValue(updateVal, type)
          }}
        />
      )

      let countryInputProps = {
        ...inputProps,
        ...adornments,
        startAdornment: countryAdornments,
      }

      FlexComponent = (
        <ReactSelect
          InputProps={countryInputProps}
          TextFieldProps={{
            variant: 'outlined',
            InputLabelProps: { ...InputLabelProps, shrink: true },
            label,
          }}
          isClearable={true}
          suggestions={countries.map((c, k) => ({ value: c.countryCode, label: c.name }))}
          selectedItems={(countryData && [{ value: countryData.countryCode, label: countryData.name }]) || []}
          customComponents={{
            MultiValue: props => <span>{props.children}</span>,
          }}
          isMulti={false}
          onChange={v => {
            let updateVal = ''
            if (v && v[0]) updateVal = dataType === 'name' ? v[0].label : v[0].value
            onChangeInputValue(updateVal, type)
          }}
        />
      )

      break

    case 'phone':
      const defaultCC = '55'

      const wa = d => {
        return d.mobileCC && d.mobile && d.mobile.toString && d.mobile.toString().replace(/\D/g, '').length >= 10
          ? parseInt(`${d.mobileCC || ''}${d.mobile || ''}`.replace(/\D/g, ''), 10)
          : ''
      }
      const waLink = d => (wa(d) ? `https://web.whatsapp.com/send?phone=${wa(d)}&text&source&d&app_absent` : '')

      const startAdornments = {
        phone: d => (
          <PhoneInput
            value={d.phoneCC || defaultCC}
            onChange={phoneCode => {
              field.handleCountryCode && field.handleCountryCode({ phoneCC: phoneCode })
            }}
          />
        ),
        mobile: d => (
          <PhoneInput
            value={d.mobileCC || defaultCC}
            onChange={phoneCode => {
              field.handleCountryCode && field.handleCountryCode({ mobileCC: phoneCode })
            }}
          />
        ),
      }

      const endAdornments = {
        mobile: d =>
          wa(d) ? (
            <a
              href={waLink(d)}
              target="_blank"
              rel="noopener noreferrer"
              onClick={e => {
                if (!d.mobileCC) {
                  e.preventDefault()
                  setAlert(langMessages['alerts.informCountryCode'])
                } else if (!wa(d)) {
                  e.preventDefault()
                  setAlert(langMessages['alerts.informFullPhoneNumber'])
                }
              }}
            >
              <IconButton size="small">
                <i className="fa fa-whatsapp text-whatsapp" />
              </IconButton>
            </a>
          ) : null,
      }

      let PhoneInputProps = { ...adornments, ...inputProps }

      switch (name) {
        case 'phone':
        case 'mobile':
          PhoneInputProps.startAdornment = name in startAdornments ? startAdornments[name](data) : null
          PhoneInputProps.endAdornment = adornments.endAdornment || (name in endAdornments ? endAdornments[name](data) : null)
          PhoneInputProps.inputComponent = TextMaskInput
          break
        default:
          break
      }

      let { variant } = { ...field, ...props }

      FlexComponent = (
        <TextField
          variant={variant || 'outlined'}
          fullWidth
          type="text"
          className={`${props.className || ''} ${inputClasses}`}
          {...inputProps}
          maxLength={maxLength}
          InputProps={PhoneInputProps}
          InputLabelProps={{ ...InputLabelProps, shrink: true }}
          name={name}
          value={currentValue}
          classes={{ root: `${props.classes || ''} FlexInput PhoneInput FlexInput-PhoneInput FlexInput-PhoneInput-${variant}` }}
          onChange={e => onChangeInputValue(e.target.value, type)}
          onFocus={e => onInputFocus({ e, val: currentValue, type })}
          onBlur={e => onInputBlur({ e, val: currentValue, type })}
        />
      )
      break

    case 'video':
      let wrapperWidth = props.wrapperWidth || field.width || '100%'
      let wrapperHeight = props.wrapperHeight || field.height || ''
      let videoSrc = props.src || field.src || ''
      let poster = props.poster || field.poster || ''

      FlexComponent = !videoSrc ? null : <VideoPlayer width={wrapperWidth} height={wrapperHeight} src={videoSrc} poster={poster} />
      break

    case 'percent':
    case 'number':
      inputValue = typeof field.min === 'number' ? Math.max(field.min, inputValue) : inputValue
      inputValue = typeof field.max === 'number' ? Math.min(field.max, inputValue) : inputValue

      FlexComponent = (
        <TextField
          variant="outlined"
          fullWidth
          type="number"
          className={`${props.className || ''} ${inputClasses}`}
          {...props}
          {...inputProps}
          InputLabelProps={{ ...InputLabelProps, shrink: true }}
          InputProps={{
            ...adornments,
            inputProps: {
              ...inputProps,
              min: typeof field.min === 'number' ? field.min : '',
              max: typeof field.max === 'number' ? field.max : '',
            },
          }}
          maxLength={maxLength}
          name={name}
          value={inputValue}
          onChange={e => onChangeInputValue(Number(e.target.value), type)}
          onFocus={e => onInputFocus({ e, val: inputValue, type })}
          onBlur={e => onInputBlur({ e, val: inputValue, type })}
        />
      )
      break
    case 'currency':
      if (!adornments.startAdornment) adornments.startAdornment = <InputAdornment position="start">$</InputAdornment>
      FlexComponent = (
        <CurrencyInput
          {...inputProps}
          // format="#.###,##"
          adornments={adornments}
          sufix={append || ''}
          prefix={prepend || '$'}
          className={`FlexInput ${props.className || ''} ${inputClasses}`}
          value={inputValue}
          min={field.min}
          max={field.max}
          onChange={val => onChangeInputValue(Number(val), type)}
          onFocus={e => onInputFocus({ e, val: inputValue, type })}
          onBlur={e => onInputBlur({ e, val: inputValue, type })}
        />
      )
      break
    case 'boolean':
    case 'true_false':
      FlexComponent = (
        <FormControlLabel
          {...props}
          control={
            <Switch
              color="secondary"
              checked={!!currentValue}
              onChange={e => onChangeInputValue(e.target.checked, type)}
              onFocus={e => onInputFocus({ e, val: currentValue, type })}
              onBlur={e => onInputBlur({ e, val: currentValue, type })}
              className="switch-btn switch-qiplus"
            />
          }
          label={field.label || ''}
          className="m-0"
        />
      )
      break
    case 'button':
    case 'Button':
      const btnColor = field.color || props.color || 'default'
      const btnVariant = field.variant || props.variant || 'contained'
      const btnClasses = field.btnClasses || props.className || ''
      const disabled = field.disabled || props.disabled || false
      FlexComponent = (
        <Button
          {...props}
          disabled={!!disabled}
          className={btnClasses}
          onClick={props.onClick || field.onClick}
          color={btnColor}
          variant={btnVariant}
        >
          {label}
        </Button>
      )
      break
    case 'button_group':
    case 'buttons':
      FlexComponent = (
        <FormGroup>
          {!!field.label && (
            <div>
              <FormLabel>
                <b>{field.label}</b>
              </FormLabel>
            </div>
          )}
          <ButtonGroup>
            {Object.keys(choices).map((val, k) => {
              const color = val === currentValue ? 'primary' : 'default'
              const variant = val === currentValue ? 'contained' : 'outlined'
              return (
                <Button {...props} color={color} variant={variant} onClick={e => onChangeInputValue(val, type)} key={val}>
                  {choices[val]}
                </Button>
              )
            })}
          </ButtonGroup>
        </FormGroup>
      )
      break
    case 'check':
    case 'checkbox':
      FlexComponent = (
        <React.Fragment>
          {!!field.label && <FormLabel component="legend">{field.label}</FormLabel>}
          <FormGroup row>
            {Object.keys(choices).map((val, k) => (
              <FormControlLabel
                {...props}
                control={
                  <Checkbox
                    color="primary"
                    checked={Array.isArray(currentValue) ? currentValue.indexOf(val) >= 0 : val === currentValue}
                    onChange={e => {
                      const newValue = Array.isArray(currentValue) ? currentValue : currentValue ? [currentValue] : []
                      if (e.target.checked) {
                        onChangeInputValue([...new Set([...newValue, e.target.value], type)])
                      } else {
                        const i = newValue.findIndex(v => v === val)
                        if (i >= 0) newValue.splice(i, 1)
                        onChangeInputValue(newValue, type)
                      }
                    }}
                    value={val}
                  />
                }
                label={choices[val] || val}
                key={val}
              />
            ))}
          </FormGroup>
        </React.Fragment>
      )

      break
    case 'datepicker':
    case 'date_picker':
    case 'datetimepicker':
      let displayFormat = props.displayFormat || field.display_format || ''
      let returnFormat = props.returnFormat || field.return_format || ''
      let minDate = 'minDate' in props ? props.minDate : 'min_date' in field ? field.min_date : ''
      let maxDate = 'maxDate' in props ? props.maxDate : 'max_date' in field ? field.max_date : ''
      let clearable = 'clearable' in props ? props.clearable : 'clearable' in field ? field.clearable : true

      defaultValue = props.defaultValue || field.default_date || ''

      if (typeof minDate === 'number') {
        const days = minDate
        minDate = new Date()
        minDate.setDate(minDate.getDate() + days)
      }
      if (typeof maxDate === 'number') {
        const days = maxDate
        maxDate = new Date()
        maxDate.setDate(maxDate.getDate() + days)
      }
      if (typeof defaultValue === 'number') {
        const days = defaultValue
        defaultValue = new Date()
        defaultValue.setDate(defaultValue.getDate() + days)
      }

      const pickerProps = {
        ...props,
      }

      if (minDate) pickerProps.minDate = minDate
      if (maxDate) pickerProps.maxDate = maxDate

      FlexComponent =
        type === 'datetimepicker' ? (
          <DTPicker
            {...pickerProps}
            label={label}
            clearable={clearable}
            defaultValue={defaultValue}
            displayFormat={displayFormat}
            returnFormat={returnFormat}
            value={currentValue}
            id={name}
            onChange={onChangeInputValue}
          />
        ) : (
          <DatePicker
            clearable={clearable}
            helperText=""
            clearLabel=""
            cancelLabel={langMessages['button.cancel']}
            {...pickerProps}
            label={label}
            id={name}
            format={displayFormat || MOMENT_SHORT}
            value={currentValue || defaultValue}
            onChange={e => onChangeInputValue(e.format(returnFormat || MOMENT_ISO), type)}
            onFocus={e => onInputFocus({ e, val: currentValue || defaultValue, type })}
            onBlur={e => onInputBlur({ e, val: currentValue || defaultValue, type })}
            InputProps={{
              endAdornment: (
                <InputAdornment position="end" className="date-picker-icon">
                  <IconButton size="small">
                    <i className="ti-calendar" />
                  </IconButton>
                </InputAdornment>
              ),
            }}
          />
        )
      break
    case 'color_picker':
      const popoverStyle = {
        // position: 'absolute',
        zIndex: '2',
        bottom: '0px',
      }
      const coverStyle = {
        // position: 'fixed',
        top: '0px',
        right: '0px',
        bottom: '0px',
        left: '0px',
      }
      FlexComponent = (
        <React.Fragment>
          <TextField
            fullWidth
            label={field.label}
            variant="outlined"
            value={currentValue}
            onFocus={e => togglePicker(!openPicker)}
            className={`${props.className || ''} ${inputClasses}`}
            InputProps={{
              className: 'w-100',
              endAdornment: (
                <span
                  onClick={e => togglePicker(!openPicker)}
                  className="color-picker-adornment"
                  style={{
                    backgroundColor: currentValue || '',
                  }}
                ></span>
              ),
            }}
          />
          {openPicker && (
            <div style={popoverStyle}>
              <div style={coverStyle} onClick={e => togglePicker('')} />
              <ChromePicker
                disableAlpha
                width={'100%'}
                triangle="hide"
                className="w-100"
                {...props}
                color={currentValue}
                onChangeComplete={color => onChangeInputValue(color.hex, type)}
              />
            </div>
          )}
        </React.Fragment>
      )
      break
    case 'ChromePicker':
      FlexComponent = <ChromePicker {...props} color={currentValue} onChangeComplete={color => onChangeInputValue(color.hex, type)} />
      break
    case 'GithubPicker':
      FlexComponent = <GithubPicker {...props} color={currentValue} onChangeComplete={color => onChangeInputValue(color.hex, type)} />
      break
    case 'BlockPicker':
      FlexComponent = <BlockPicker {...props} color={currentValue} onChangeComplete={color => onChangeInputValue(color.hex, type)} />
      break
    case 'radio':
      if (props.inputProps) delete props.inputProps

      FlexComponent =
        (choices && Object.keys(choices).length && (
          <React.Fragment>
            {!!field.label && <FormLabel component="legend">{field.label}</FormLabel>}
            <RadioGroup
              row
              {...props}
              name={name}
              value={currentValue}
              onChange={e => !readonly && onChangeInputValue(e.target.value, type)}
              onFocus={e => onInputFocus({ e, val: currentValue, type })}
              onBlur={e => onInputBlur({ e, val: currentValue, type })}
            >
              {Object.keys(choices).map((val, k) => {
                return <FormControlLabel {...inputProps} key={val} label={choices[val]} value={val} control={<Radio color="primary" />} />
              })}
            </RadioGroup>
          </React.Fragment>
        )) ||
        null
      break
    case 'user':
    case 'users':
    case 'UsersSelector':
      isMulti = !!props.multi || !!field.multi || !!field.multiple
      field.fetchedUsers && (props.fetchedUsers = field.fetchedUsers)
      FlexComponent = collection && (
        <UsersSelector
          {...props}
          filterUsers={props.filterUsers || field.filter || (p => p)}
          collection={collection}
          queries={queries}
          label={label}
          multi={isMulti}
          roles={field.roles || props.roles || []}
          clearable={field.clearable || props.clearable}
          TextFieldProps={{
            ...inputProps,
            ...(props.TextFieldProps || {}),
          }}
          values={currentValue ? (Array.isArray(currentValue) && currentValue) || [currentValue] : []}
          onChange={values => {
            const singleVal = (values && values[0] && values[0].value) || ''
            const updateVal = isMulti ? (values && values.map(v => v.value)) || [] : singleVal
            onChangeInputValue(updateVal, type)
          }}
        />
      )
      break
    case 'UsersDualListBox':
      FlexComponent = collection && (
        <UsersDualListBox
          {...props}
          collection={collection}
          label={label}
          users={props.users || []}
          values={currentValue ? (Array.isArray(currentValue) && currentValue) || [currentValue] : []}
          onChange={values => onChangeInputValue(values, type)}
        />
      )
      break
    case 'post_object':
    case 'PostsSelector':
      isMulti = !!props.multi || !!field.multi
      field.fetchedPosts && (props.fetchedPosts = field.fetchedPosts)
      FlexComponent = collection && (
        <PostsSelector
          {...props}
          filterPosts={props.filterPosts || field.filter || (p => p)}
          collection={collection}
          queries={queries}
          label={label}
          multi={isMulti}
          clearable={field.clearable || props.clearable}
          TextFieldProps={{
            ...inputProps,
            ...(props.TextFieldProps || {}),
          }}
          values={currentValue ? (Array.isArray(currentValue) && currentValue) || [currentValue] : []}
          onChange={values => {
            const singleVal = (values && values[0] && values[0].value) || ''
            const updateVal = isMulti ? (values && values.map(v => v.value)) || [] : singleVal
            onChangeInputValue(updateVal, type)
          }}
        />
      )
      break
    case 'tags':
    case 'taxonomy':
      const taxName = type === 'tags' ? type : name || field.taxonomy
      FlexComponent = collection && (
        <Taxonomy
          {...props}
          filterTaxonomies={props.filterTaxonomies || field.filter || (p => p)}
          label={label}
          taxName={taxName}
          collection={collection}
          queries={queries}
          clearable={field.clearable || props.clearable}
          values={currentValue ? (Array.isArray(currentValue) && currentValue) || [currentValue] : []}
          onUpdate={values => onChangeInputValue(values, type)}
        />
      )
      break
    case 'relationship':
    case 'duallistbox':
    case 'DualListBox':
      isMulti = props.multi !== false && field.multi !== false
      FlexComponent = (choices || collection) && (
        <DualListBox
          autoHeightMin={200}
          filterInput
          {...props}
          collection={collection || ''}
          maxPosts={field.max || 0}
          minPosts={field.min || 0}
          choices={choices || []}
          values={currentValue ? (Array.isArray(currentValue) && currentValue) || [currentValue] : []}
          onChange={ids => {
            const singleVal = (ids && ids[0] && `${ids[0]}`) || ''
            const updateVal = isMulti ? ids || [] : singleVal
            onChangeInputValue(updateVal, type)
          }}
        />
      )
      break
    case 'listbox':
    case 'ListBox':
      isMulti = props.multi !== false && field.multi !== false
      FlexComponent = (choices || collection) && (
        <ListBox
          autoHeightMin={200}
          filterInput
          {...props}
          collection={collection || ''}
          maxPosts={field.max || 0}
          minPosts={field.min || 0}
          choices={choices || []}
          values={currentValue ? (Array.isArray(currentValue) && currentValue) || [currentValue] : []}
          onChange={ids => {
            const singleVal = (ids && ids[0] && `${ids[0]}`) || ''
            const updateVal = isMulti ? ids || [] : singleVal
            onChangeInputValue(updateVal, type)
          }}
        />
      )
      break
    case 'editor':
    case 'wysiwyg':
    case 'SimpleEditor':
      FlexComponent = (
        <div>
          {!!field.label && (
            <div>
              <FormLabel>
                <b>{field.label}</b>
              </FormLabel>
            </div>
          )}
          <SimpleEditor
            {...props}
            value={currentValue}
            onChange={message => {
              onChangeInputValue(message, type)
            }}
          />
        </div>
      )
      break
    case 'slider':
      let sliderOptions = field.options || props.options || []
      FlexComponent = (
        <div>
          {!!field.label && (
            <div>
              <FormLabel>
                <b>{field.label}</b>
              </FormLabel>
            </div>
          )}
          <CustomSlider
            valueLabelDisplay="auto"
            // valueLabelFormat={(value) => <i className="fa fa-user"/>}
            min={field.min || 0}
            max={field.max || window.jsonClone(sliderOptions).pop().value}
            step={field.min || null}
            {...props}
            value={Number(currentValue)}
            onChange={(event, newValue) => onChangeInputValue(newValue, type)}
            marks={sliderOptions}
          />
        </div>
      )
      break
    case 'select':
      FlexComponent = (
        <TextField
          variant="outlined"
          fullWidth
          className={`${props.className || ''} ${inputClasses}`}
          {...inputProps}
          select
          name={name}
          value={currentValue}
          onChange={e => onChangeInputValue(e.target.value, type)}
          onFocus={e => onInputFocus({ e, val: inputValue, type: fieldType })}
          onBlur={e => onInputBlur({ e, val: inputValue, type: fieldType })}
        // margin="normal"
        >
          {Object.keys(choices).map((value, k) => (
            <MenuItem key={k} value={value}>
              {choices[value]}
            </MenuItem>
          )) || []}
        </TextField>
      )
      break
    case 'SelectInput':
    case 'autocomplete':
    case 'Autocomplete':
      let groupBy = ''
      let groups = props.groups || field.groups || {}
      if (choices && Object.keys(groups).length === Object.keys(choices).length) {
        groupBy = 'group'
      }

      let options = field.options || Object.keys(choices).map(value => ({ label: choices[value], value, group: groups[value] || '' })) || []
      let renderOption = field.renderOption || props.renderOption || undefined

      FlexComponent = (
        <div>
          <SelectInput
            {...props}
            TextFieldProps={{
              ...inputProps,
            }}
            name={name}
            groupBy={groupBy}
            options={options}
            value={currentValue}
            renderOption={renderOption}
            onChange={option => onChangeInputValue(option !== null ? option.value : option, type)}
          />
        </div>
      )
      break
    case 'images':
    case 'ImagesModule':
      // FlexComponent = (
      //     <ImagesModule
      //         // {...props}
      //         images={currentValue||{}}
      //         collection={collection}
      //         postId={postId}
      //         imageSizes={props.imageSizes||field.image_sizes}
      //         onUpdate={(imagesObj) => {
      //             if (field.key) {
      //                 onChangeInputValue({ [field.key]: imagesObj.images })
      //                 onChange({ imageSizes: imagesObj.imageSizes })
      //                 return
      //             }
      //             onChangeInputValue(imagesObj)
      //         }}
      //     />
      // )

      break
    case 'custom':
    case 'Custom':
      const { custom } = field
      if (!custom) return null

      FlexComponent = custom
      break
    case 'domain':
      const { domain } = field
      fieldType = 'text'

      let adornment = {}
      if (append && domain) {
        adornment = { endAdornment: <InputAdornment position="end">{`@${domain}`}</InputAdornment> }
        inputValue = inputValue.includes('@') ? inputValue.split('@')[0] : inputValue
      }

      InputProps = { ...adornment, ...inputProps }
      rows = null
      const onChange = e => {
        let val = e.target.value
        if (append && domain && val) {
          onChangeInputValue(`${val}@${domain}`, fieldType)
        } else {
          onChangeInputValue(e.target.value, fieldType)
        }
      }
      FlexComponent = (
        <TextField
          variant="outlined"
          fullWidth
          type={fieldType}
          multiline={false}
          className={`${props.className || ''} ${inputClasses}`}
          {...props}
          {...inputProps}
          InputProps={InputProps}
          InputLabelProps={InputLabelProps}
          maxLength={maxLength}
          name={name}
          value={inputValue}
          onChange={onChange}
        />
      )
      break

    default:
      return null
  }

  return (
    <React.Fragment>
      {!!alertMsg && (
        <SweetAlert show={true} title="Ooops" onConfirm={() => setAlert('')} btnSize="sm" type="warning">
          {alertMsg}
        </SweetAlert>
      )}
      {FlexComponent}
    </React.Fragment>
  )
}

export default FlexInput
