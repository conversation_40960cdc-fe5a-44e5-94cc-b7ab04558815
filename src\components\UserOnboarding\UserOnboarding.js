import { <PERSON>, Button, Dialog, DialogActions, Dialog<PERSON>ontent, IconButton, Typography } from '@material-ui/core'
import CloseIcon from '@material-ui/icons/Close'
import React, { useEffect, useState } from 'react'
import { QIUSERS_COLLECTION_NAME } from '../../constants/AppCollections'
import { FirebaseRepository } from '../../firebase/repository'
import { localJSON } from '../../helpers/helpers'
import { langMessages } from '../../lang/index'

export function UserOnboarding(props) {
  const { isOpenOnboarding } = props

  const user = localJSON.get('user', {})
  const repository = new FirebaseRepository()
  const userpath = `${QIUSERS_COLLECTION_NAME}/${user.id}`

  const checkOpen = () => {
    if (onboardingState === 'finished' || isOpenOnboarding === false) {
      return false
    }
    return true
  }

  const [open, setOpen] = useState(checkOpen())
  const [onboardingState, setOnboardingState] = useState(user?.onboardingState?.toString() || 'funil')

  const onBoardingValues = {
    funil: {
      title: 'Passo a Passo Funil de Vendas',
      link: 'https://qiplus.com.br/wp-content/uploads/Aula-Inicial-Criar-Funil-de-Vendas-2.mp4',
      reference: 'funil',
    },
    landingPage: {
      title: 'Passo a Passo Criação de Landing Page',
      link: 'https://qiplus.com.br/wp-content/uploads/Landing-Page-1.mp4',
      reference: 'landingPage',
    },
    form: {
      title: 'Passo a Passo Criação de Formulário',
      link: 'https://qiplus.com.br/wp-content/uploads/Aula-Inicial-Criar-Formulario-2.mp4',
      reference: 'form',
    },
    automation: {
      title: 'Passo a Passo Criação de Automação',
      link: 'https://youtu.be/6sQ9knT-_SI?si=P_PV9Qa2g68muiS4',
      reference: 'automation',
    },
  }

  const handleClose = () => {
    setOpen(false)
  }

  useEffect(() => {
  }, [onboardingState])

  const handleNext = index => {
    const nextOnboarding = Object.values(onBoardingValues).find((value, i) => i === index + 1)
    if (!nextOnboarding) {
      handleClose()
      localJSON.set('user', { onboardingState: 'finished' })

      repository.setDoc(userpath, { onboardingState: 'finished' })

      return
    }
    setOnboardingState(nextOnboarding.reference)
    repository.setDoc(userpath, { onboardingState: nextOnboarding.reference })

  }

  const handlePrevious = index => {
    const previousOnboarding = Object.values(onBoardingValues).find((value, i) => i === index - 1)
    if (!previousOnboarding) {
      handleClose()
      return
    }
    setOnboardingState(previousOnboarding.reference)
    repository.setDoc(userpath, { onboardingState: previousOnboarding.reference })
  }

  return (
    Object.values(onBoardingValues).map((value, index) => {
      if (onboardingState === value.reference) {
        return (
          <Dialog
            key={index}
            onClose={handleClose}
            aria-labelledby="customized-dialog-title"
            open={open}
            fullWidth={true}
            maxWidth={'md'}
            scroll="paper"
            PaperProps={{
              style: {
                minHeight: '80%',
                maxHeight: '80%',
              },
            }}
          >
            <Box
              sx={{
                display: 'flex',
                justifyContent: 'space-between',
                p: 1,
                m: 1,
                bgcolor: 'background.paper',
              }}
            >
              <Typography>{value.title}</Typography>

              <IconButton
                aria-label="close"
                onClick={handleClose}
                sx={{
                  position: 'absolute',
                  right: 8,
                  top: 8,
                  color: theme => theme.palette.grey[500],
                }}
              >
                <CloseIcon />
              </IconButton>
            </Box>
            <DialogContent>
              {
                value.title !== 'Passo a Passo Criação de Automação' ?
                  <embed src={value.link} name={value.title} width='100%' style={{ height: '60vh', border: 'none' }}></embed> :
                  <iframe width='100%' style={{ height: '60vh', border: 'none' }} src="https://www.youtube.com/embed/YkNmdARVnyQ?si=Xz2Ivl8N5lUvXKtJ" title="YouTube video player" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share" referrerpolicy="strict-origin-when-cross-origin" allowfullscreen></iframe>
              }
            </DialogContent>
            <DialogActions>
              <Button variant="contained" disabled={index === 0} onClick={() => handlePrevious(index)} color="primary">
                {langMessages['tables.previousText']}
              </Button>
              <Button variant="contained" onClick={() => handleNext(index)} color="primary">
                {langMessages['button.next']}
              </Button>
            </DialogActions>
          </Dialog>
        )
      }
    }))
}
