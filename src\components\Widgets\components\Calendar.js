/**
 * CalendarWidget
 */
import React from 'react'

import AppModules from 'Constants/AppModules'

import FullCalendar from '@fullcalendar/react'
import dayGridPlugin from '@fullcalendar/daygrid'
import timeGridPlugin from '@fullcalendar/timegrid'
import listPlugin from '@fullcalendar/list'
import timelinePlugin from '@fullcalendar/timeline'
import resourcePlugin from '@fullcalendar/resource-timeline'
import interactionPlugin from '@fullcalendar/interaction' // needed for dayClick
import googleCalendarPlugin from '@fullcalendar/google-calendar'
import allLocales from '@fullcalendar/core/locales-all'

const Calendar = ({
  events,
  gCalendarSources,
  views,
  defaultView,
  onClickDate,
  onSelectSlot,
  onSelectEvent,
  onDragStart,
  onDragStop,
  onEventResize,
  ...props
}) => {
  const calendarComponentRef = React.createRef()

  let calendarApi
  React.useEffect(() => {
    calendarApi = calendarComponentRef.current.getApi()
  })

  let [inactiveSources, toggleSources] = React.useState([])

  let eventSources = []
  let calendarButtons = {}
  let calendarLabels = {}

  let eventsByType = {}
  events.forEach(e => {
    let srcId = e.collection || 'default'
    !eventsByType[srcId] && (eventsByType[srcId] = [])
    eventsByType[srcId].push(e)
  })

  Object.keys(eventsByType).forEach((srcId, i) => {
    eventSources.push({
      id: srcId,
      events: eventsByType[srcId],
      active: true,
      label: AppModules[srcId] ? AppModules[srcId].label : calendarLabels[srcId] || srcId,
      collection: srcId,
      className: 'qiplus-event',
    })
  })

  Array.isArray(gCalendarSources) &&
    gCalendarSources.forEach(data => {
      const { id, color } = data
      eventSources.push({
        id,
        googleCalendarId: id,
        editable: false,
        active: true,
        backgroundColor: color || '#FF2342',
        borderColor: color || '#FF2342',
        textColor: '#FFFFFF',
        label: 'Google',
        collection: 'google',
        className: 'gcal-event',
      })
    })

  eventSources.forEach(src => {
    const { id, label, collection } = src
    if (calendarButtons[collection]) {
      calendarButtons[collection].sourceIds.push(id)
      return
    }
    calendarButtons[collection] = {
      data: src,
      sourceIds: [id],
      text: label,
      click: function (e) {
        e.target.classList.toggle('inactive')
        let { sourceIds } = calendarButtons[collection]
        // let updatedSources = window.jsonClone(inactiveSources);
        sourceIds.forEach(sId => {
          let eventSource = calendarApi.getEventSourceById(sId)
          if (eventSource) {
            eventSource.remove()
          } else {
            eventSource = eventSources.find(s => s.id === sId)
            calendarApi.addEventSource(eventSource)
          }
          // updatedSources = updatedSources.includes(sId) ? updatedSources.filter(aId=>aId!==sId) : [ ...updatedSources, sId ]
        })
        // toggleSources(updatedSources);
      },
    }
  })

  let calendarButtonsKeys = Object.keys(calendarButtons)

  return (
    <FullCalendar
      ref={calendarComponentRef}
      editable
      selectable
      header={
        props.header || {
          left: 'prev,next today',
          center: 'title',
          right: views || `${calendarButtonsKeys.join(',')} dayGridMonth,timeGridWeek,timeGridDay,listWeek`,
        }
      }
      // events={events}
      eventSources={eventSources.filter(s => !inactiveSources.includes(s.id))}
      customButtons={calendarButtons}
      firstDay={1}
      locales={allLocales}
      locale={props.locale || 'pt-br'}
      defaultView={defaultView || 'timeGridWeek'}
      defaultDate={props.defaultDate || new Date()}
      plugins={[dayGridPlugin, timeGridPlugin, listPlugin, timelinePlugin, resourcePlugin, interactionPlugin, googleCalendarPlugin]}
      dateClick={date => onClickDate && onClickDate(date)}
      select={slot => onSelectSlot && onSelectSlot(slot)}
      eventClick={info => onSelectEvent && onSelectEvent(info)}
      eventDragStart={info => onDragStart && onDragStart(info)}
      eventDrop={info => onDragStop && onDragStop(info)}
      eventResize={info => onEventResize && onEventResize(info)}
      googleCalendarApiKey={'AIzaSyBz9w00GIXHu_lB6K_45PVPH_TNRZl_cF8'}
      schedulerLicenseKey={'GPL-My-Project-Is-Open-Source'}
      {...props}
    />
  )
}

export default Calendar
