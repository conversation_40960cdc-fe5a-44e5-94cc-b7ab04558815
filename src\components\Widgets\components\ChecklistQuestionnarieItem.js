import React, { useState } from 'react'
import { Checkbox, FormGroup, FormControlLabel, FormControl, Radio, Select, MenuItem, RadioGroup, Box } from '@material-ui/core'

import { Progress } from 'reactstrap'

function ChecklistQuestionnarieItem(props) {
  const { choices, type, label, disabled, metrics } = props
  const [choiceValue, setChoice] = useState(choices.find(c => c.selected)?.value || '')
  const [choiceValueRadio, setChoiceRadio] = useState(choices.findIndex(c => c.selected))
  const [checkboxState, setCheckboxState] = useState(
    choices.map(choice => {
      return {
        value: choice.value,
        check: choice.selected,
      }
    })
  )

  const handleChange = event => {
    setChoice(event.target.value)
    choices.map((choice, i) => {
      choice.selected = choice.value === event.target.value
    })
    props.metricCalc()
  }

  const handleChangeRadio = event => {
    const index = parseInt(event.target.value, 10)
    setChoiceRadio(index)
    choices.map((choice, i) => {
      choice.selected = i === index
    })
  }

  const handleUpdateChecked = (choice, event) => {
    choice.selected = !choice.selected
    const checkbox = checkboxState
    const checkboxIndex = checkbox.findIndex(c => c.value === choice.value)
    const newCheckboxState = checkbox.map((c, i) => {
      if (i === checkboxIndex) {
        return {
          ...c,
          check: !c.selected,
        }
      } else {
        return {
          ...c,
        }
      }
    })
    setCheckboxState(newCheckboxState)
  }

  let totalScore = 0
  let completedScore = 0
  let completedProgress = 0
  let color = 'info'

  try {
    let questionsSelecteds = choices.filter(choice => choice?.selected === true)

    questionsSelecteds.map(question => (completedScore += parseInt(question.score, 10)))

    if (type === 'checkbox') {
      choices.map(choice => {
        totalScore += parseInt(choice.score, 10)
      })
    } else {
      let higherNumber = 0

      choices.map(choice => {
        higherNumber = Math.max(parseInt(choice.score, 10), higherNumber)
      })

      totalScore = higherNumber
    }
    completedProgress = parseInt((completedScore / totalScore) * 100, 10)

    if (metrics && metrics.good !== '') {
      if (completedScore >= metrics.good) {
        color = 'success'
      } else if (completedScore >= metrics.medium) {
        color = 'warning'
      } else {
        color = 'danger'
      }
    }

    props.metricCalc()
  } catch (error) {}

  return (
    <div className="col my-10">
      <Box style={{ padding: 5, margin: 12 }}>
        {label && <h4>{label}</h4>}

        <div className="d-flex tasklist-progress">
          <div className="flex-0 text-base mr-5">
            {
              <small>
                {completedScore} / {totalScore} Pontos
              </small>
            }
          </div>
          <div className="flex-1">
            <Progress animated color={color} className="my-10 progress-xs" value={completedProgress} />
          </div>
        </div>

        {type === 'checkbox' &&
          choices.map((choice, i) => {
            return (
              <div key={'checkbox-' + i}>
                <FormGroup>
                  <FormControlLabel
                    control={<Checkbox />}
                    value={!!choice.value}
                    label={choice.value}
                    checked={!!choice.selected}
                    onChange={value => {
                      handleUpdateChecked(choice, value)
                    }}
                    disabled={disabled}
                  />
                </FormGroup>
              </div>
            )
          })}

        {type === 'radio' && (
          <FormControl>
            {choices.map((choice, i) => {
              return (
                <RadioGroup
                  aria-label="gender"
                  key={i}
                  name="gender1"
                  defaultValue={choiceValueRadio}
                  value={choiceValueRadio}
                  onChange={handleChangeRadio}
                >
                  <div key={'radio-' + i}>
                    <FormControlLabel key={'radio' + i} control={<Radio />} value={i} label={choice.value} disabled={disabled} />
                  </div>
                </RadioGroup>
              )
            })}
          </FormControl>
        )}

        {type === 'select' && (
          <FormControl fullWidth>
            <Select value={choiceValue} onChange={handleChange} className="mt-5">
              {choices.map(choice => {
                return (
                  <MenuItem key={choice.value} style={{ width: '100%' }} value={choice.value}>
                    {choice.value}
                  </MenuItem>
                )
              })}
            </Select>
          </FormControl>
        )}
      </Box>
    </div>
  )
}

export default ChecklistQuestionnarieItem
