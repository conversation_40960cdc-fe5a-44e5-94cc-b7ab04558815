import React from 'react'

// lang strings
import { langMessages } from '../../../lang'

import ReactSelect from './ReactSelect'

import { Chip } from '@material-ui/core'

import CancelIcon from '@material-ui/icons/Cancel'

const TagsSelect = ({ tags, selectedTags, onChange, isLoading, isClearable, isMulti, placeholder, isFilter, size }) => {
  return (
    <ReactSelect
      isClearable={isClearable !== false}
      isMulti={isMulti !== false}
      isDisabled={!tags.length || !!isLoading}
      isLoading={!!isLoading}
      placeholder={placeholder || (isFilter ? `${langMessages['texts.filterBy']} ${langMessages['modules.tags']}` : langMessages['modules.tags'])}
      suggestions={tags.map(t => ({
        value: t.ID,
        label: t.title,
        color: t.color || 'primary',
      }))}
      selectedItems={(selectedTags || [])
        .filter(tagId => tags.find(t => t.ID === tagId))
        .map(tagId => ({
          value: tags.find(t => t.ID === tagId).ID,
          label: tags.find(t => t.ID === tagId).title,
          color: tags.find(t => t.ID === tagId).color || 'primary',
        }))}
      TextFieldProps={{
        size: size || null,
      }}
      customComponents={{
        MultiValue: props => (
          <Chip
            tabIndex={-1}
            label={props.children}
            className={`bg-${props.data.color || 'primary'} text-white mr-1`}
            onDelete={props.removeProps.onClick}
            deleteIcon={<CancelIcon className="text-white" {...props.removeProps} />}
          />
        ),
      }}
      onChange={values => {
        if (values && values.length) {
          let newTags = values.map(v => v.value)
          onChange(newTags)
          return
        }
        onChange(isMulti !== false ? [] : '')
      }}
    />
  )
}

export default TagsSelect
