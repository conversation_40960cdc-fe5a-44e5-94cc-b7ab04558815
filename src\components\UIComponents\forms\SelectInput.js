/* eslint-disable no-use-before-define */
import React from 'react'
import TextField from '@material-ui/core/TextField'
import Autocomplete from '@material-ui/lab/Autocomplete'

const SelectInput = ({ options, value, onChange, TextFieldProps, ...props }) => {
  const thisProps = {}
  if (props.groupBy) {
    thisProps.groupBy = option => option[props.groupBy]
  }
  if (props.renderOption) {
    thisProps.renderOption = props.renderOption
  }
  return (
    <Autocomplete
      options={options}
      value={options.find(o => o.value === value) || null}
      onChange={(e, option) => onChange(option)}
      getOptionLabel={option => option.label}
      {...thisProps}
      // style={{ width: 300 }}
      renderInput={params => <TextField fullWidth variant="outlined" {...(TextFieldProps || {})} {...params} />}
    />
  )
}

// Top 100 films as rated by IMDb users. http://www.imdb.com/chart/top
// const top100Films = [
//     { label: '3 Idiots', value: 2009, year: 2009 },
//     { label: 'Monty Python and the Holy Grail', value: 1975, year: 1975 },
// ];

export default SelectInput
