# Troubleshooting 400 Bad Request Error - WhatsApp Groups API

Este guia ajuda a diagnosticar e resolver o erro 400 (Bad Request) que você está enfrentando ao criar grupos do WhatsApp.

## 🔍 Erro Observado

```
🗝️ Valid Token
🗝️ Creating 1 groups for 1 leads (max 500 per group)
🗝️ Group creation error: {
  status: 400,
  error: 'Bad Request',
  response: { message: [ [Array] ] }
}
```

## 🛠️ Diagnóstico Melhorado

Com as atualizações implementadas, agora você verá logs mais detalhados:

```bash
🗝️ Phone validation summary: { total: 1, valid: 1, invalid: 0 }
🗝️ Sending request to Evolution API:
- URL: /group/create/account_instance
- Body: {
    "subject": "Group Name",
    "description": "Description",
    "participants": ["*************"]
  }
🗝️ Group creation error details:
- Status: 400
- Error: Bad Request
- Response data: { "message": ["detailed error array"] }
- Request body sent: { ... }
- Request URL: /group/create/account_instance
```

## 🔧 Passos para Diagnóstico

### 1. Execute o Script de Debug

```bash
node debug-group-creation.js
```

Este script testará:
- ✅ Diferentes formatos de telefone
- ✅ Validação de nomes de grupo
- ✅ Requisição mínima válida
- ✅ Teste direto da Evolution API (se credenciais disponíveis)

### 2. Verifique os Logs Detalhados

Com as melhorias implementadas, você verá:

```bash
# Validação de telefones
🗝️ Phone validation summary: { total: 1, valid: 1, invalid: 0 }

# Corpo da requisição enviado
🗝️ Sending request to Evolution API:
- URL: /group/create/account123_instance123
- Body: {
    "subject": "Test Group",
    "description": "",
    "participants": ["*************"]
  }

# Detalhes do erro (se houver)
🗝️ Group creation error details:
- Status: 400
- Error: Bad Request
- Response data: {
    "message": [
      "participants.0 must be a valid phone number",
      "subject must not be empty"
    ]
  }
```

### 3. Causas Comuns do Erro 400

#### A. Formato de Telefone Inválido

**Problema**: Evolution API pode esperar formato específico

**Soluções**:
```javascript
// ✅ Formatos válidos testados
"*************"     // Brasileiro com código do país
"+*************"    // Com sinal de +
"***********"       // Sem código do país (será normalizado)

// ❌ Formatos inválidos
"(11) 99999-9999"   // Com formatação
"11 99999-9999"     // Com espaços
"abc123"            // Com letras
```

#### B. Instance ID Incorreto

**Problema**: Evolution API pode esperar formato específico do instance ID

**Verificação**:
```bash
# Verifique se o instance existe na Evolution API
curl -X GET "https://your-evolution-api.com/instance/fetchInstances?instanceName=account_instance" \
  -H "apikey: your-api-key"
```

#### C. Campos Obrigatórios Ausentes

**Problema**: Evolution API pode exigir campos específicos

**Verificação**:
```javascript
// Campos mínimos obrigatórios
{
  "subject": "Nome do Grupo",        // ✅ Obrigatório
  "description": "",                 // ✅ Pode ser vazio
  "participants": ["*************"]  // ✅ Array com pelo menos 1 telefone
}
```

#### D. Autenticação/Headers Incorretos

**Problema**: Headers da Evolution API podem estar incorretos

**Verificação**:
```javascript
// Headers corretos para Evolution API
{
  "Content-Type": "application/json",
  "apikey": "your-evolution-api-key"  // Não "Authorization: Bearer"
}
```

## 🔍 Testes Específicos

### Teste 1: Validação de Telefone

```bash
# Teste com diferentes formatos
curl -X POST "http://localhost:8000/whatsapp/groups/create/instance123" \
  -H "Authorization: Bearer your-token" \
  -H "Content-Type: application/json" \
  -d '{
    "accountId": "account123",
    "userId": "user123",
    "groupName": "Test Group",
    "members": ["*************"]
  }'
```

### Teste 2: Evolution API Direta

```bash
# Teste direto na Evolution API
curl -X POST "https://your-evolution-api.com/group/create/instance123" \
  -H "Content-Type: application/json" \
  -H "apikey: your-api-key" \
  -d '{
    "subject": "Test Group",
    "description": "Test description",
    "participants": ["*************"]
  }'
```

### Teste 3: Verificar Instance

```bash
# Verificar se a instance existe
curl -X GET "https://your-evolution-api.com/instance/connectionState/instance123" \
  -H "apikey: your-api-key"
```

## 🛠️ Soluções por Cenário

### Cenário 1: Telefone Inválido

```javascript
// ❌ Antes
"members": ["(11) 99999-9999"]

// ✅ Depois (normalizado automaticamente)
"members": ["*************"]
```

### Cenário 2: Instance ID Incorreto

```javascript
// ❌ Formato incorreto
const instanceName = "instance123"

// ✅ Formato correto (account_instance)
const instanceName = makeInstanceName(accountId, instanceId)
// Resultado: "account123_instance123"
```

### Cenário 3: Headers Incorretos

```javascript
// ❌ Headers incorretos
headers: {
  "Authorization": "Bearer token",
  "Content-Type": "application/json"
}

// ✅ Headers corretos para Evolution API
headers: {
  "apikey": "your-evolution-api-key",
  "Content-Type": "application/json"
}
```

## 🔧 Configuração de Debug

### 1. Ativar Logs Detalhados

```bash
# No seu .env
DEBUG=true
LOG_LEVEL=debug
```

### 2. Usar Mock para Teste

```bash
# Para testar sem Evolution API
USE_WHATSAPP_MOCK=true
```

### 3. Verificar Configuração

```javascript
// Verificar se as variáveis estão corretas
console.log('WHATSAPP_API_URL:', process.env.WHATSAPP_API_URL)
console.log('WHATSAPP_API_TOKEN:', process.env.WHATSAPP_API_TOKEN ? '[SET]' : '[NOT SET]')
```

## 📋 Checklist de Verificação

- [ ] ✅ Instance ID existe na Evolution API
- [ ] ✅ Token da Evolution API está correto
- [ ] ✅ URL da Evolution API está acessível
- [ ] ✅ Telefones estão em formato internacional
- [ ] ✅ Nome do grupo não está vazio
- [ ] ✅ Headers da requisição estão corretos
- [ ] ✅ Instance está conectada no WhatsApp

## 🚀 Próximos Passos

1. **Execute o script de debug**: `node debug-group-creation.js`
2. **Analise os logs detalhados** no console
3. **Teste a Evolution API diretamente** se possível
4. **Verifique a configuração** das variáveis de ambiente
5. **Compartilhe os logs detalhados** para análise mais específica

## 💡 Dicas Adicionais

- Use `USE_WHATSAPP_MOCK=true` para testar sem a Evolution API
- Verifique se a instance está conectada no WhatsApp
- Confirme se o número de telefone está registrado no WhatsApp
- Teste com apenas 1 participante primeiro
- Verifique se há rate limiting na Evolution API

Com essas melhorias, você deve conseguir identificar exatamente o que está causando o erro 400! 🔍
