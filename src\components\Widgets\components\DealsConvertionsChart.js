import React from 'react'

import CountUp from 'react-countup'
import { langMessages } from '../../../lang'

import { currencyFormat, getDeepValue, isset } from '../../../helpers/helpers'

const DealsConvertionsChart = ({ stats, contactsData, filters, investmentAmount, ...other }) => {
  let leadsCount = Object.keys(contactsData).filter(cId => !!cId).length

  let dealsCount = getDeepValue(stats, ['deals', 'deals_added', 'total'], 0)
  let dealsWonCount = getDeepValue(stats, ['deals', 'won', 'total'], 0)

  let conversionLeads = []
  if (isset(stats, ['tickets', 'tickets_added', 'contactIds']))
    conversionLeads = [...new Set([...stats.tickets.tickets_added.contactIds, ...conversionLeads])]
  let uniqueContactsConvertionCount = conversionLeads.length

  let ticketsCount = getDeepValue(stats, ['tickets', 'tickets_added', 'total'], 0)
  let ticketsTotal = getDeepValue(stats, ['tickets', 'tickets_added', 'accumulators', 'total'], 0)

  let dealsWonRate = dealsCount ? Number((dealsWonCount / dealsCount) * 100).toFixed(2) : 0
  let ticketsAvg = ticketsCount ? Number(ticketsTotal / ticketsCount).toFixed(2) : 0
  let netRevenue = ticketsTotal - investmentAmount

  let ROI = investmentAmount && netRevenue ? Number((netRevenue / investmentAmount) * 100).toFixed(2) : 0
  let CAC = uniqueContactsConvertionCount && investmentAmount ? Number(investmentAmount / uniqueContactsConvertionCount).toFixed(0) : 0

  const layers = []

  layers.push({
    label: langMessages['stats.visitors'],
    count: Number(leadsCount),
    aside: {
      label: langMessages['stats.conversionCost'],
      value: `$${currencyFormat(CAC)}/lead`,
    },
  })
  layers.push({
    label: langMessages['stats.leadsCap'],
    count: Number(uniqueContactsConvertionCount),
  })
  layers.push({
    label: langMessages['stats.opportunities'],
    count: Number(dealsCount),
    aside: {
      label: langMessages['stats.dealsWon'],
      value: `${dealsWonRate}%`,
    },
  })
  layers.push({
    label: langMessages['stats.dealsWon'],
    count: Number(dealsWonCount),
  })
  layers.push({
    label: langMessages['stats.ticketsAddedCount'],
    count: Number(ticketsCount),
    aside: {
      label: langMessages['stats.ticketsAverage'],
      value: `$${currencyFormat(ticketsAvg)}`,
    },
  })
  layers.push({
    label: langMessages['stats.ticketsTotal'],
    value: `$${currencyFormat(ticketsTotal)}`,
  })
  layers.push({
    label: langMessages['stats.netRevenue'],
    value: `$${currencyFormat(netRevenue)}`,
  })

  return (
    <div className="conversion-stats-wrapper">
      {layers.map(({ label, value, count, aside }, k) => {
        return (
          <div className={'conversion-stats'} key={k}>
            <h4>{label}</h4>
            {!!value && <span>{value}</span>}
            {typeof count === 'number' && (
              <span>
                <CountUp start={0} end={count} />
              </span>
            )}
            {!!aside && (
              <div className={'conversion-stats-aside border-warning'}>
                <h5>{aside.label}</h5>
                <span>{aside.value}</span>
              </div>
            )}
          </div>
        )
      })}
    </div>
  )
}

export default DealsConvertionsChart
