/**
 * List Dividers
 */
import React from 'react'
import { FormControlLabel, Switch } from '@material-ui/core'

const ToggableDivider = ({ label, pos, hrClasses, customClasses, first, labelrops, checked, toggle, ...props }) => (
  <React.Fragment>
    <div
      className={`LabeledDividerComponent ToggableDividerComponent labeled-divider label-to-${pos || 'left'} ${first ? 'first' : ''} ${customClasses || ''}`}
    >
      <hr className={hrClasses || ''} />
      <span className="label" {...labelrops}>
        {label || ''}
      </span>
      <FormControlLabel
        className="m-0"
        control={<Switch color={props.color || 'primary'} checked={!!checked} onChange={e => toggle()} className="switch-btn switch-qiplus" />}
        // labelPlacement="start"
        // label={langMessages["time.deadline"]}
      />
    </div>
    {props.children}
  </React.Fragment>
)
export default ToggableDivider
