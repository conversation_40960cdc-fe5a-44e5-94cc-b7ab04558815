import moment from 'moment'
import React from 'react'
import CountUp from 'react-countup'
import CONSTANTS from '../../../constants/AppConstants'

import { langMessages } from '../../../lang'

import campaignModel from '../../../routes/campaigns/model'

import { isset, getDeepValue } from '../../../helpers/helpers'

import { Tooltip } from '@material-ui/core'

const BasicCampaignReportWidget = ({ stats, contactsData, filters, campaign, ...other }) => {
  const { data } = campaign

  const investments = (data || {}).investments || window.jsonClone(campaignModel.data.investments)
  const investmentsItems = investments || []
  const investmentsTotal = investmentsItems.reduce((all, b) => all + b.value, 0)

  let leadsIds = Object.keys(contactsData).filter(cId => !!cId)
  let leadsCount = leadsIds.length
  let addedLeadsCount = getDeepValue(stats, ['leads', 'leads_added', 'total'], 0)

  let conversionLeads = []
  // if (isset(stats,['tickets','tickets_added','contactIds'])) conversionLeads = [...new Set([ ...stats.tickets.tickets_added.contactIds, ...conversionLeads ])]
  if (isset(stats, ['funnels', 'converted', 'contactIds']))
    conversionLeads = [...new Set([...stats.funnels.converted.contactIds, ...conversionLeads])]
  if (isset(stats, ['automations', 'converted', 'contactIds']))
    conversionLeads = [...new Set([...stats.automations.converted.contactIds, ...conversionLeads])]
  let uniqueContactsConvertionCount = conversionLeads.length

  let ticketsTotal = 0
  // ticketsTotal += isset(stats,['tickets','tickets_added','accumulators','total']) ? stats.tickets.tickets_added.accumulators.total : 0
  ticketsTotal += isset(stats, ['funnels', 'converted', 'accumulators', 'total']) ? stats.funnels.converted.accumulators.total : 0
  ticketsTotal += isset(stats, ['automations', 'converted', 'accumulators', 'total']) ? stats.automations.converted.accumulators.total : 0

  let netRevenue = ticketsTotal - investmentsTotal

  let ROI = investmentsTotal && netRevenue ? Number((netRevenue / investmentsTotal) * 100).toFixed(2) : 0
  let CAC = uniqueContactsConvertionCount && investmentsTotal ? Number(investmentsTotal / uniqueContactsConvertionCount).toFixed(0) : 0

  let LTV = 0
  let LTVTotal = 0

  leadsIds.forEach(cId => {
    let added = getDeepValue(contactsData[cId], ['leads', 'leads_added', 'axis', 0], '')
    let conversionTotal = 0
    // let conversionTotal = getDeepValue(contactsData[cId],["tickets","tickets_added","accumulators","total"],0)

    if (!added) return

    let conversionDates = []
    let conversionsCount = 0
    // if (isset(contactsData[cId],["tickets","tickets_added","axis"])) {
    //     let ticketsStats = contactsData[cId].tickets.tickets_added.axis
    //     let ticketsDates = Object.keys(ticketsStats)
    //     conversionsCount += ticketsDates.reduce((all, dateStamp)=>all+ticketsStats[dateStamp],0)
    //     conversionDates = [ ...conversionDates, ...ticketsDates ]
    // }

    if (isset(contactsData[cId], ['funnels', 'converted', 'axis'])) {
      let funnelsStats = contactsData[cId].funnels.converted.axis
      let funnelsDates = Object.keys(funnelsStats)
      conversionsCount += funnelsDates.reduce((all, dateStamp) => all + funnelsStats[dateStamp], 0)
      conversionDates = [...conversionDates, ...funnelsDates]
      conversionTotal += getDeepValue(contactsData[cId], ['funnels', 'converted', 'accumulators', 'total'], 0)
    }

    if (isset(contactsData[cId], ['automations', 'converted', 'axis'])) {
      let automationsStats = contactsData[cId].automations.converted.axis
      let automationsDates = Object.keys(automationsStats)
      conversionsCount += automationsDates.reduce((all, dateStamp) => all + automationsStats[dateStamp], 0)
      conversionDates = [...conversionDates, ...automationsDates]
      conversionTotal += getDeepValue(contactsData[cId], ['automations', 'converted', 'accumulators', 'total'], 0)
    }

    conversionDates.sort((a, b) => moment(a).valueOf() - moment(b).valueOf())

    let firstDate = added
    let lastDate = moment(filters.end || undefined).format(CONSTANTS.MOMENT_ISO)
    let daysBetween = moment(lastDate).diff(moment(firstDate), 'days')
    let monthsBetween = daysBetween / 30
    let conversionsAvg = conversionTotal / (conversionsCount || 1)

    let leadLTV = conversionsAvg / (monthsBetween || 1)

    LTVTotal += leadLTV
    LTV = LTVTotal / leadsCount

    // console.log({ cId, conversionDates, conversionTotal, conversionsCount, conversionsAvg, firstDate, lastDate, daysBetween, monthsBetween, leadLTV, LTV });
  })

  return (
    <div className="report-status">
      <ul className="list-inline d-flex align-content-center">
        <li className="list-inline-item col">
          <h4>
            <span className={'ladgend badge-success mr-10'}>&nbsp;</span> {'ROI'}
            <Tooltip classes={{ tooltip: 'font-xs bg-dark' }} placement="right-start" title={langMessages['stats.tooltip.ROI']}>
              <small className="align-top">
                <i className="fa fa-question-circle ml-5" />
              </small>
            </Tooltip>
          </h4>
          <h2 className="title">
            <CountUp start={0} end={Number(ROI)} duration={3} />% &nbsp;
            <small>
              <small className="text-base float-right">
                {`R$${Number(netRevenue).toFixed(0)}`}/<small>{`R$${Number(investmentsTotal).toFixed(0)}`}</small>
              </small>
            </small>
          </h2>
          {/* <h4>9 Order</h4> */}
        </li>
        <li className="list-inline-item col">
          <h4>
            <span className={'ladgend badge-warning mr-10'}>&nbsp;</span> {'CAC'}
            <Tooltip classes={{ tooltip: 'font-xs bg-dark' }} placement="right-start" title={langMessages['stats.tooltip.CAC']}>
              <small className="align-top">
                <i className="fa fa-question-circle ml-5" />
              </small>
            </Tooltip>
          </h4>
          <h2 className="title">
            R$
            <CountUp start={0} end={Number(CAC)} duration={3} /> &nbsp;
            <small>
              <small className="text-base float-right">
                {`R$${Number(investmentsTotal).toFixed(0)}`}/
                <small>
                  {uniqueContactsConvertionCount} {langMessages['texts.customers'].toLowerCase()}
                </small>
              </small>
            </small>
          </h2>
          {/* <h4>9 Order</h4> */}
        </li>
        <li className="list-inline-item col">
          <h4>
            <span className={'ladgend badge-info mr-10'}>&nbsp;</span> {'LTV'}
            <Tooltip classes={{ tooltip: 'font-xs bg-dark' }} placement="right-start" title={langMessages['stats.tooltip.LTV']}>
              <small className="align-top">
                <i className="fa fa-question-circle ml-5" />
              </small>
            </Tooltip>
          </h4>
          <h2 className="title">
            R$
            <CountUp start={0} end={Number(Number(LTV).toFixed(2))} duration={3} /> <small>/{langMessages['calendar.month'].toLowerCase()}</small>{' '}
            &nbsp;
            <small>
              <small className="text-base float-right">
                <small>
                  {leadsCount} {langMessages['modules.leads'].toLowerCase()}
                </small>
              </small>
            </small>
          </h2>
          {/* <h4>9 Order</h4> */}
        </li>
      </ul>
    </div>
  )
}

export default BasicCampaignReportWidget
