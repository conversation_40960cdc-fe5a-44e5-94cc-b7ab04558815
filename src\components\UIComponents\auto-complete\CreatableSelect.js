import React, { Component } from 'react'

import CreatableSelect from 'react-select/creatable'

const createOption = label => ({
  label,
  value: label.toLowerCase().replace(/\W/g, ''),
})

const defaultOptions = [
  createOption('One'),
  createOption('Two'),
  createOption('Three'),
]

class CreatableSelect extends Component {
  state = {
    isLoading: false,
    options: defaultOptions,
    value: undefined,
  }

  handleChange = (newValue, actionMeta) => {
    this.setState({ value: newValue })
  }

  handleCreate = inputValue => {
    this.setState({ isLoading: true })

    setTimeout(() => {
      const { options } = this.state
      const newOption = createOption(inputValue)

      this.setState({
        isLoading: false,
        options: [...options, newOption],
        value: newOption,
      })
    }, 1000)
  }

  render() {
    const { isLoading, options, value } = this.state
    return (
      <CreatableSelect
        isClearable
        isMulti
        isDisabled={isLoading}
        isLoading={isLoading}
        onChange={this.handleChange}
        onCreateOption={this.handleCreate}
        options={options}
        value={value}
      />
    )
  }
}

export default CreatableSelect
