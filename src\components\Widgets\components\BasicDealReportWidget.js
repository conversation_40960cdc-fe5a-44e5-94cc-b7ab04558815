import React from 'react'
import CountUp from 'react-countup'

import { currencyFormat, getDeepValue } from '../../../helpers/helpers'

import { langMessages } from '../../../lang'

const BasicDealReportWidget = ({ stats, ...other }) => {
  let dealsCount = getDeepValue(stats, ['deals', 'deals_added', 'total'], 0)
  let wonCount = getDeepValue(stats, ['deals', 'won', 'total'], 0)
  let lostCount = getDeepValue(stats, ['deals', 'lost', 'total'], 0)

  let dealsValue = getDeepValue(stats, ['deals', 'deals_added', 'accumulators', 'value'], 0)
  let wonValue = getDeepValue(stats, ['deals', 'won', 'accumulators', 'value'], 0)
  let lostValue = getDeepValue(stats, ['deals', 'lost', 'accumulators', 'value'], 0)

  let dealsWonRate = dealsCount ? Number((wonCount / dealsCount) * 100).toFixed(2) : 0
  let dealsLostRate = dealsCount ? Number((lostCount / dealsCount) * 100).toFixed(2) : 0

  return (
    <div className="report-status">
      <ul className="list-inline d-flex align-content-center">
        <li className="list-inline-item col">
          <h4>
            <span className={'ladgend badge-primary mr-10'}>&nbsp;</span> {langMessages['stats.dealsAdded']}
          </h4>
          <h2 className="title">
            <CountUp start={0} end={Number(dealsCount)} duration={1} /> &nbsp;
            <small className="text-base float-right">
              <small>R${currencyFormat(dealsValue)}</small>
            </small>
          </h2>
        </li>
        <li className="list-inline-item col">
          <h4>
            <span className={'ladgend badge-success mr-10'}>&nbsp;</span> {langMessages['stats.dealsWon']}
          </h4>
          <h2 className="title">
            <CountUp start={0} end={Number(wonCount)} duration={1} /> &nbsp;{' '}
            <small className="text-base">
              <small>{dealsWonRate}%</small>
            </small>
            <small className="text-base float-right">
              <small>R${currencyFormat(wonValue)}</small>
            </small>
          </h2>
        </li>
        <li className="list-inline-item col">
          <h4>
            <span className={'ladgend badge-danger mr-10'}>&nbsp;</span> {langMessages['stats.dealsLost']}
          </h4>
          <h2 className="title">
            <CountUp start={0} end={Number(lostCount)} duration={1} /> &nbsp;{' '}
            <small className="text-base">
              <small>{dealsLostRate}%</small>
            </small>
            <small className="text-base float-right">
              <small>R${currencyFormat(lostValue)}</small>
            </small>
          </h2>
        </li>
      </ul>
    </div>
  )
}

export default BasicDealReportWidget
