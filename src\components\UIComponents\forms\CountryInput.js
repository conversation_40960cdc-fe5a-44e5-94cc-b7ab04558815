import React from 'react'

// lang strings
import { langMessages } from '../../../lang'

import ReactFlagsSelect from 'react-flags-select'
import 'react-flags-select/scss/react-flags-select.scss'

import countries from '../../../assets/data/countries'

const CountryInput = ({ value, onChange, ...props }) => {
  let countryCode = (countries.find(c => c.countryCode.toLowerCase() === (value || '').toLowerCase()) || {}).countryCode || 'BR'

  const inputEl = React.useRef(null)
  let [cc, setCC] = React.useState(countryCode)

  React.useEffect(() => {
    setCC(countryCode)
    try {
      inputEl.current.updateSelected(countryCode)
    } catch (error) {
      console.error(error)
    }
  }, [countryCode])

  return (
    <React.Fragment>
      <ReactFlagsSelect
        searchable={true}
        searchPlaceholder="Selecione..."
        defaultCountry={cc}
        showSelectedLabel={false}
        onSelect={v => {
          let countryData = countries.find(c => c.countryCode === v)
          onChange(countryData)
          setCC(v)
        }}
        ref={inputEl}
        {...props}
      />
    </React.Fragment>
  )
}

export default CountryInput
