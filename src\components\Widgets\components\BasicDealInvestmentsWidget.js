import React from 'react'
import moment from 'moment'

import CONSTANTS from '../../../constants/AppConstants'

import CountUp from 'react-countup'
import { langMessages } from '../../../lang'

import { Tooltip } from '@material-ui/core'
import { getDeepValue, isset } from '../../../helpers/helpers'

const BasicDealInvestmentsWidget = ({ stats, contactsData, filters, investmentAmount, ...other }) => {
  let leadsIds = Object.keys(contactsData).filter(cId => !!cId)
  let leadsCount = leadsIds.length

  let conversionLeads = []
  if (isset(stats, ['tickets', 'tickets_added', 'contactIds']))
    conversionLeads = [...new Set([...stats.tickets.tickets_added.contactIds, ...conversionLeads])]
  let uniqueContactsConvertionCount = conversionLeads.length

  let ticketsTotal = getDeepValue(stats, ['tickets', 'tickets_added', 'accumulators', 'total'], 0)
  let netRevenue = ticketsTotal - investmentAmount

  let ROI = investmentAmount && netRevenue ? Number((netRevenue / investmentAmount) * 100).toFixed(2) : 0
  let CAC = uniqueContactsConvertionCount && investmentAmount ? Number(investmentAmount / uniqueContactsConvertionCount).toFixed(0) : 0

  let LTV = 0
  let LTVTotal = 0

  leadsIds.forEach(cId => {
    let added = Object.keys(getDeepValue(contactsData[cId], ['leads', 'leads_added', 'axis'], {}))[0]
    let conversionTotal = getDeepValue(contactsData[cId], ['tickets', 'tickets_added', 'accumulators', 'total'], 0)

    if (!added) return

    let conversionDates = []
    let conversionsCount = 0
    if (isset(contactsData[cId], ['tickets', 'tickets_added', 'axis'])) {
      let ticketsStats = contactsData[cId].tickets.tickets_added.axis
      let ticketsDates = Object.keys(ticketsStats)
      conversionsCount += ticketsDates.reduce((all, dateStamp) => all + ticketsStats[dateStamp], 0)
      conversionDates = [...conversionDates, ...ticketsDates]
    }

    conversionDates.sort((a, b) => moment(a).valueOf() - moment(b).valueOf())

    let firstDate = added
    let lastDate = moment(filters.end || undefined).format(CONSTANTS.MOMENT_ISO)
    let daysBetween = moment(lastDate).diff(moment(firstDate), 'days')
    let monthsBetween = daysBetween / 30
    let conversionsAvg = conversionTotal / (conversionsCount || 1)

    let leadLTV = conversionsAvg / (monthsBetween || 1)

    LTVTotal += leadLTV
    LTV = LTVTotal / leadsCount

    // delayLog({ cId, conversionDates, conversionTotal, conversionsCount, conversionsAvg, firstDate, lastDate, daysBetween, monthsBetween, leadLTV, LTV },'delayLog > LTV > '+cId);
  })

  return (
    <div className="report-status">
      <ul className="list-inline d-flex align-content-center">
        <li className="list-inline-item col">
          <h4>
            <span className={'ladgend badge-success mr-10'}>&nbsp;</span> {'ROI'}
            <Tooltip classes={{ tooltip: 'font-xs bg-dark' }} placement="right-start" title={langMessages['stats.tooltip.ROI']}>
              <small className="align-top">
                <i className="fa fa-question-circle ml-5" />
              </small>
            </Tooltip>
          </h4>
          <h2 className="title">
            <CountUp start={0} end={Number(ROI)} duration={1} />% &nbsp;
            <small>
              <small className="text-base float-right">
                {`R$${Number(netRevenue).toFixed(0)}`}/<small>{`R$${Number(investmentAmount).toFixed(0)}`}</small>
              </small>
            </small>
          </h2>
          {/* <h4>9 Order</h4> */}
        </li>
        <li className="list-inline-item col">
          <h4>
            <span className={'ladgend badge-warning mr-10'}>&nbsp;</span> {'CAC'}
            <Tooltip classes={{ tooltip: 'font-xs bg-dark' }} placement="right-start" title={langMessages['stats.tooltip.CAC']}>
              <small className="align-top">
                <i className="fa fa-question-circle ml-5" />
              </small>
            </Tooltip>
          </h4>
          <h2 className="title">
            R$
            <CountUp start={0} end={Number(CAC)} duration={1} /> &nbsp;
            <small>
              <small className="text-base float-right">
                {`R$${Number(investmentAmount).toFixed(0)}`}/
                <small>
                  {uniqueContactsConvertionCount} {langMessages['texts.conversions'].toLowerCase()}
                </small>
              </small>
            </small>
          </h2>
          {/* <h4>9 Order</h4> */}
        </li>
        <li className="list-inline-item col">
          <h4>
            <span className={'ladgend badge-info mr-10'}>&nbsp;</span> {'LTV'}
            <Tooltip classes={{ tooltip: 'font-xs bg-dark' }} placement="right-start" title={langMessages['stats.tooltip.LTV']}>
              <small className="align-top">
                <i className="fa fa-question-circle ml-5" />
              </small>
            </Tooltip>
          </h4>
          <h2 className="title">
            R$
            <CountUp start={0} end={Number(Number(LTV).toFixed(2))} duration={1} /> <small>/{langMessages['calendar.month'].toLowerCase()}</small>{' '}
            &nbsp;
            <small>
              <small className="text-base float-right">
                <small>
                  {leadsCount} {langMessages['modules.leads'].toLowerCase()}
                </small>
              </small>
            </small>
          </h2>
          {/* <h4>9 Order</h4> */}
        </li>
      </ul>
    </div>
  )
}

export default BasicDealInvestmentsWidget
