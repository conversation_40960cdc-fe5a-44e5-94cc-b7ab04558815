/**
 * Linear Interminate ProgressBar
 */
import React from 'react'

// rct section loader
import RctSectionLoader from 'Components/RctSectionLoader/RctSectionLoader'

function SelectPlaceholder({ children, fetching, classes, size, pos, label, noChoices, noChoicesLabel, ...props }) {
  classes = classes || {}
  return (
    <div className={`SelectPlaceholderComponent SelectPlaceholderComponent-wrapper size-${size || 'normal'} pos-${pos || 'end'}`}>
      {fetching ? (
        <div className={`SelectPlaceholderComponent-loader ${classes.loaderWrapper || ''}`}>
          <RctSectionLoader className={classes.loader || ''} size={size || ''} pos={pos || 'end'} />
        </div>
      ) : noChoices ? (
        <div className="SelectPlaceholderComponent-alert alert alert-warning mb-0">{noChoicesLabel}</div>
      ) : (
        children
      )}
      {(fetching || noChoices) && !!label && <span className="SelectPlaceholderComponent-label">{label}</span>}
    </div>
  )
}

export default SelectPlaceholder
