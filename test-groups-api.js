/**
 * Test script for WhatsApp Groups API (QIP-526)
 *
 * This script demonstrates how to use the new group creation endpoints
 * Run with: node test-groups-api.js
 */

const axios = require('axios')

// Configuration
const BASE_URL = 'http://localhost:8000' // Adjust to your server URL
const INSTANCE_ID = 'test-instance-123'
const ACCOUNT_ID = 'test-account-123'
const USER_ID = 'test-user-123'
const AUTH_TOKEN = 'your-auth-token-here' // Replace with actual token

// Sample leads data (simulating the frontend selection)
const sampleLeads = [
  {
    id: 'lead1',
    firstName: '<PERSON>',
    lastName: '<PERSON>',
    phone: '*********9999',
    displayName: '<PERSON>'
  },
  {
    id: 'lead2',
    firstName: 'Maria',
    lastName: '<PERSON>',
    phone: '*************',
    displayName: '<PERSON>'
  },
  {
    id: 'lead3',
    firstName: '<PERSON>',
    lastName: '<PERSON>',
    phone: '*************',
    displayName: '<PERSON>'
  },
  // Add more leads to test group division
  ...Array.from({ length: 1200 }, (_, i) => ({
    id: `lead${i + 4}`,
    firstName: `User${i + 4}`,
    lastName: 'Test',
    phone: `*********${String(i + 4).padStart(4, '0')}`,
    displayName: `User${i + 4} Test`
  }))
]

async function testCreateMultipleGroups() {
  console.log('🧪 Testing WhatsApp Groups API - Create Multiple Groups')
  console.log(`📊 Testing with ${sampleLeads.length} leads`)
  console.log(`📱 Expected groups: ${Math.ceil(sampleLeads.length / 500)}`)

  try {
    const response = await axios.post(
      `${BASE_URL}/whatsapp/groups/create-multiple/${INSTANCE_ID}`,
      {
        accountId: ACCOUNT_ID,
        userId: USER_ID,
        groupPrefix: 'Test Group',
        maxLeadsPerGroup: 500,
        leads: sampleLeads
      },
      {
        headers: {
          'Authorization': `Bearer ${AUTH_TOKEN}`,
          'Content-Type': 'application/json'
        }
      }
    )

    console.log('✅ API Response:')
    console.log(JSON.stringify(response.data, null, 2))

    if (response.data.data.success) {
      console.log(`🎉 Successfully created ${response.data.data.createdGroups.length} groups`)
      console.log(`❌ Failed to create ${response.data.data.failedGroups.length} groups`)
    }

  } catch (error) {
    console.error('❌ Error testing create multiple groups:', error.response?.data || error.message)
  }
}

async function testListGroups() {
  console.log('\n🧪 Testing WhatsApp Groups API - List Groups')

  try {
    const response = await axios.get(
      `${BASE_URL}/whatsapp/groups/list/${INSTANCE_ID}?accountId=${ACCOUNT_ID}`,
      {
        headers: {
          'Authorization': `Bearer ${AUTH_TOKEN}`
        }
      }
    )

    console.log('✅ Groups List:')
    console.log(JSON.stringify(response.data, null, 2))

  } catch (error) {
    console.error('❌ Error testing list groups:', error.response?.data || error.message)
  }
}

async function testShotXEndpoints() {
  console.log('\n🧪 Testing ShotX Groups API')

  try {
    // Test ShotX create multiple groups
    const createResponse = await axios.post(
      `${BASE_URL}/shotx/groups/create-multiple`,
      {
        instanceId: INSTANCE_ID,
        accountId: ACCOUNT_ID,
        userId: USER_ID,
        groupPrefix: 'ShotX Test Group',
        maxLeadsPerGroup: 500,
        leads: sampleLeads.slice(0, 100) // Test with fewer leads
      },
      {
        headers: {
          'Content-Type': 'application/json'
        }
      }
    )

    console.log('✅ ShotX Create Response:')
    console.log(JSON.stringify(createResponse.data, null, 2))

    // Test ShotX list groups
    const listResponse = await axios.get(
      `${BASE_URL}/shotx/groups/list/${INSTANCE_ID}?accountId=${ACCOUNT_ID}`
    )

    console.log('✅ ShotX List Response:')
    console.log(JSON.stringify(listResponse.data, null, 2))

  } catch (error) {
    console.error('❌ Error testing ShotX endpoints:', error.response?.data || error.message)
  }
}

async function runTests() {
  console.log('🚀 Starting WhatsApp Groups API Tests (QIP-526)')
  console.log('🔗 Evolution API Integration with Mock Fallback')
  console.log('=' .repeat(50))

  // Test the main functionality
  await testCreateMultipleGroups()

  // Wait a bit before next test
  await new Promise(resolve => setTimeout(resolve, 1000))

  // Test listing groups
  await testListGroups()

  // Wait a bit before next test
  await new Promise(resolve => setTimeout(resolve, 1000))

  // Test ShotX integration
  await testShotXEndpoints()

  console.log('\n✨ Tests completed!')
  console.log('\n📝 Notes:')
  console.log('- ✅ Real Evolution API integration implemented')
  console.log('- 🔧 Mock service only activates when USE_WHATSAPP_MOCK=true')
  console.log('- 🏢 Development environment uses real API (homologation)')
  console.log('- 🗝️ Evolution API endpoint: POST /group/create/{instance}')
  console.log('- 📊 Supports up to 5000 leads divided into groups of 500')
  console.log('- ⏱️ 2-second delay between group creations to avoid rate limits')
  console.log('- 🛡️ Full validation and error handling implemented')
  console.log('\n🔧 To use mock service: set USE_WHATSAPP_MOCK=true')
}

// Run tests if this file is executed directly
if (require.main === module) {
  runTests().catch(console.error)
}

module.exports = {
  testCreateMultipleGroups,
  testListGroups,
  testShotXEndpoints,
  runTests
}
