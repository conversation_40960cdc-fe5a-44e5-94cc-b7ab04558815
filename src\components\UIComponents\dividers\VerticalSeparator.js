/**
 * List Dividers
 */
import React from 'react'

const VerticalSeparator = ({ children, pos, ...props }) => (
  <div className={`position-relative p${pos === 'after' ? 'r' : 'l'}-${props.width || 60}`}>
    {pos === 'after' && children}
    <div
      className="vertical-separator position-absolute"
      style={{ top: 0, bottom: 0, left: pos === 'after' ? `calc(100% - ${(props.width || 60) / 2}px)` : (props.width || 60) / 2 }}
    ></div>
    {pos !== 'after' && children}
  </div>
)

export default VerticalSeparator
