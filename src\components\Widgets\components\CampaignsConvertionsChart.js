import React from 'react'

import CountUp from 'react-countup'
import { langMessages } from '../../../lang'

import { currencyFormat, getDeepValue } from '../../../helpers/helpers'

const CampaignsConvertionsChart = ({ stats, contactsData, filters, investmentAmount, ...other }) => {
  let leadsTotal = getDeepValue(stats, ['leads', 'leads_added', 'total'], 0)
  let leadsAvg = getDeepValue(stats, ['leads', 'leads_added', 'daily'], 0)
  let sentTotal = getDeepValue(stats, ['campaigns', 'sent', 'total'], 0)
  let openTotal = getDeepValue(stats, ['campaigns', 'opened', 'total'], 0)
  let sessionsTotal = getDeepValue(stats, ['landing-pages', 'visited', 'total'], 0)
  let ticketsCount = getDeepValue(stats, ['tickets', 'tickets_added', 'total'], 0)
  let ticketsTotal = getDeepValue(stats, ['tickets', 'tickets_added', 'accumulators', 'total'], 0)

  let openRate = sentTotal ? Number((openTotal / sentTotal) * 100).toFixed(2) : 0
  let clickRate = openTotal ? Number((sessionsTotal / openTotal) * 100).toFixed(2) : 0
  let conversionRate = sessionsTotal ? Number((ticketsCount / sessionsTotal) * 100).toFixed(2) : 0
  let ticketsAvg = ticketsCount ? Number(ticketsTotal / ticketsCount).toFixed(2) : 0

  const layers = []

  layers.push({
    label: langMessages['stats.leadsRegistered'],
    count: Number(leadsTotal),
  })
  layers.push({
    label: langMessages['stats.leadsDailyCap'],
    count: Number(leadsAvg),
  })
  layers.push({
    label: langMessages['stats.emailsSentCount'],
    count: Number(sentTotal),
    aside: {
      label: langMessages['stats.emailsOpenRate'],
      value: `${openRate}%`,
    },
  })
  layers.push({
    label: langMessages['stats.emailsOpenCount'],
    count: Number(openTotal),
    aside: {
      label: langMessages['stats.emailsClickRate'],
      value: `${clickRate}%`,
    },
  })
  layers.push({
    label: langMessages['stats.sessionsCount'],
    count: Number(sessionsTotal),
    aside: {
      label: langMessages['stats.conversionRate'],
      value: `${conversionRate}%`,
    },
  })
  layers.push({
    label: langMessages['stats.ticketsAddedCount'],
    count: Number(ticketsCount),
    aside: {
      label: langMessages['stats.ticketsAverage'],
      value: `$${currencyFormat(ticketsAvg)}`,
    },
  })
  layers.push({
    label: langMessages['stats.ticketsTotal'],
    value: `$${currencyFormat(ticketsTotal)}`,
  })

  return (
    <div className="conversion-stats-wrapper">
      {layers.map(({ label, value, count, aside }, k) => {
        return (
          <div className={'conversion-stats'} key={k}>
            <h4>{label}</h4>
            {!!value && <span>{value}</span>}
            {typeof count === 'number' && (
              <span>
                <CountUp start={0} end={count} />
              </span>
            )}
            {!!aside && (
              <div className={'conversion-stats-aside border-warning'}>
                <h5>{aside.label}</h5>
                <span>{aside.value}</span>
              </div>
            )}
          </div>
        )
      })}
    </div>
  )
}

export default CampaignsConvertionsChart
