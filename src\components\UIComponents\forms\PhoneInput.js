import React from 'react'

// lang strings
import { langMessages } from '../../../lang'

import ReactFlagsSelect from 'react-flags-select'
import 'react-flags-select/scss/react-flags-select.scss'

import countries from '../../../assets/data/countries'

const PhoneInput = ({ value, onChange, ...props }) => {
  let countryCode = (countries.find(c => c.phoneCode === value) || {}).countryCode || 'BR'

  const inputEl = React.useRef(null)
  let [cc, setCC] = React.useState(countryCode)

  React.useEffect(() => {
    setCC(countryCode)
    try {
      inputEl.current.updateSelected(countryCode)
    } catch (error) {
      console.error(error)
    }
  }, [countryCode])

  return (
    <React.Fragment>
      <ReactFlagsSelect
        searchable={true}
        searchPlaceholder="Selecione..."
        defaultCountry={cc}
        showSelectedLabel={false}
        onSelect={countryCode => {
          let phoneCode = countries.find(c => c.countryCode === countryCode).phoneCode
          onChange(phoneCode)
          setCC(countryCode)
        }}
        ref={inputEl}
        {...props}
      />
    </React.Fragment>
  )
}

export default PhoneInput
