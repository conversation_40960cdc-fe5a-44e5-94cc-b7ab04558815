import React from 'react'
import CountUp from 'react-countup'

import { getDeepValue } from '../../../helpers/helpers'

import { langMessages } from '../../../lang'

const BasicLandingPageReportWidget = ({ stats, ...other }) => {
  let sessionsCount = getDeepValue(stats, ['landing-pages', 'visited', 'total'], 0)
  let addedLeadsCount = getDeepValue(stats, ['leads', 'leads_added', 'total'], 0)

  let ipStats = getDeepValue(stats, ['landing-pages', 'visited', 'counters', 'remote_addr'], [])
  let uniqueIPs = ipStats.length

  const referrersStats = getDeepValue(stats, ['landing-pages', 'visited', 'counters', 'referrer'], [])
  let uniqueReferrers = referrersStats.length

  return (
    <div className="report-status">
      <ul className="list-inline d-flex align-content-center">
        <li className="list-inline-item col">
          <h4>
            <span className={'ladgend badge-success mr-10'}>&nbsp;</span> {langMessages['stats.sessionsCount']}
          </h4>
          <h2 className="title">
            <CountUp start={0} end={Number(sessionsCount)} duration={3} /> &nbsp;
            <small>
              <small className="text-base float-right">
                <small>
                  {uniqueIPs} {langMessages['stats.uniqueVisitors'].toLowerCase()}
                </small>
              </small>
            </small>
          </h2>
        </li>
        <li className="list-inline-item col">
          <h4>
            <span className={'ladgend badge-warning mr-10'}>&nbsp;</span> {langMessages['stats.leadsRegistered']}
          </h4>
          <h2 className="title">
            <CountUp start={0} end={Number(addedLeadsCount)} duration={3} /> {langMessages['modules.leads'].toLowerCase()} &nbsp;
            <small>
              <small className="text-base float-right">
                <small>{}</small>
              </small>
            </small>
          </h2>
        </li>
        <li className="list-inline-item col">
          <h4>
            <span className={'ladgend badge-info mr-10'}>&nbsp;</span> {langMessages['stats.trafficSources']}
          </h4>
          <h2 className="title">
            <CountUp start={0} end={Number(uniqueReferrers)} duration={3} /> {langMessages['texts.sources'].toLowerCase()} &nbsp;
            <small>
              <small className="text-base float-right">
                <small>{}</small>
              </small>
            </small>
          </h2>
        </li>
      </ul>
    </div>
  )
}

export default BasicLandingPageReportWidget
