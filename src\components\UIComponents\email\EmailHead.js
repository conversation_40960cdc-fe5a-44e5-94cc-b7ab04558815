import React from 'react'

// lang strings
import { langMessages } from '../../../lang'

import { Avatar } from '@material-ui/core'

const EmailHead = ({ email, subject, from, fromName, to, cc, bcc, ...props }) => {
  return (
    <div
      className="email-head-preview"
      style={{
        background: '#fbfbfb',
        padding: '30px',
        boxShadow: '0 1px 20px rgba(0,0,0,0.1)',
        margin: '-15px -15px 40px -15px',
      }}
    >
      <h1>{subject || ''}</h1>
      <div className="d-flex py-15">
        <div className="p-15">
          <div className="avatar">
            <Avatar className={'bg-primary rounded-circle'}>{fromName.charAt(0)}</Avatar>
          </div>
        </div>
        <div className="head flex-1 pr-30">
          <div className="from">
            <small>{langMessages['email.head.from']}: </small>
            <b>{fromName}</b>
            {!!from && <small>&lt;{from}&gt;</small>}
          </div>
          <div className="border-top to">
            <small>{langMessages['email.head.to']}: </small>
            <span>{to}</span>
          </div>
          {!!cc && (
            <div className="border-top cc">
              <small>{langMessages['email.head.cc']}: </small>
              <span>{cc}</span>
            </div>
          )}
          {!!bcc && (
            <div className="border-top bcc">
              <small>{langMessages['email.head.bcc']}: </small>
              <span>{bcc}</span>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

export default EmailHead
