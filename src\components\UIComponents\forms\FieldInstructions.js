import React from 'react'

const FieldInstructions = ({ wrapperClasses, children, ...props }) => {
  return (
    <div className={`field-instructions line-10 mt-5 ${wrapperClasses || ''}`}>
      {typeof children === 'string' ? (
        children.split(/(?:\r\n|\r|\n)/).map((str, k) => {
          return (
            <div key={k}>
              <small>{str}</small>
            </div>
          )
        })
      ) : (
        <small>{children}</small>
      )}
    </div>
  )
}

export default FieldInstructions
