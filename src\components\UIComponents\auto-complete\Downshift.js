/**
 * Downshift Component
 */
import React from 'react'
import TextField from '@material-ui/core/TextField'
import MenuItem from '@material-ui/core/MenuItem'
import Downshift from 'downshift'

// rct card box
import RctCollapsibleCard from 'Components/RctCollapsibleCard/RctCollapsibleCard'

function renderInput(inputProps) {
  const { InputProps, classes, ref, ...other } = inputProps

  return (
    <TextField
      {...other}
      inputRef={ref}
      InputProps={{
        ...InputProps,
      }}
    />
  )
}

function renderSuggestion(params) {
  const { suggestion, index, itemProps, highlightedIndex, selectedItem } = params
  const isHighlighted = highlightedIndex === index
  const isSelected = selectedItem === suggestion.label

  return (
    <MenuItem
      {...itemProps}
      key={suggestion.label}
      selected={isHighlighted}
      component="div"
      style={{
        fontWeight: isSelected ? 500 : 400,
      }}
    >
      {suggestion.label}
    </MenuItem>
  )
}

function getSuggestions(inputValue, suggestions) {
  let count = 0
  return suggestions.filter(suggestion => {
    const keep = (!inputValue || suggestion.label.toLowerCase().includes(inputValue.toLowerCase())) && count < 5
    if (keep) {
      count += 1
    }
    return keep
  })
}

export default function IntegrationDownshift(props) {
  const { classes, suggestions, placeholder } = props
  return (
    <Downshift>
      {({ getInputProps, getItemProps, isOpen, inputValue, selectedItem, highlightedIndex }) => (
        <div>
          {renderInput({
            fullWidth: true,
            classes,
            InputProps: getInputProps({
              placeholder: placeholder || '',
              id: 'integration-downshift',
            }),
          })}
          {isOpen ? (
            <RctCollapsibleCard square>
              {getSuggestions(inputValue, suggestions).map((suggestion, index) =>
                renderSuggestion({
                  suggestion,
                  index,
                  itemProps: getItemProps({ item: suggestion.label }),
                  highlightedIndex,
                  selectedItem,
                })
              )}
            </RctCollapsibleCard>
          ) : null}
        </div>
      )}
    </Downshift>
  )
}
