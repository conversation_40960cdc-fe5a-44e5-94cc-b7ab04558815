import axios, { AxiosResponse } from 'axios'
import { makeInstanceName } from '../../../utils/codechatUtils'
import { waitMiliseconds } from '../../../utils/time'
import MessageReadType from '../../types/codechat/messageRead'
import { InstanceCreateResponse } from '../../types/evolution/instance/response/create'
import { WhatsappServiceReponseType } from '../../types/whatsapp/Response'
import { CreateGroupResponse } from '../../types/whatsapp/group'
const { WHATSAPP_API_TOKEN, WHATSAPP_API_URL, NODE_ENV } = process.env
export class EvolutionService {
  private axios: any
  constructor() {
    this.axios = axios.create({
      baseURL: WHATSAPP_API_URL,
      headers: {
        'Content-Type': 'application/json',
        Apikey: WHATSAPP_API_TOKEN,
      },
    })
  }

  setHeadersJWT(jwt: string | null) {
    if (jwt) {
      this.axios.defaults.headers.put.Authorization = `Bearer ${jwt}`
    }
  }

  async createInstance(
    instanceId: string,
    accountId: string
  ): Promise<WhatsappServiceReponseType<InstanceCreateResponse>> {
    const instanceName = makeInstanceName(accountId, instanceId)
    console.log('🗝️ Creating instance', instanceName)
    const url = `/instance/create`
    const body = {
      instanceName,
      groupsIgnore: true,
      integration: 'WHATSAPP-BAILEYS',
      webhook: {
        url: `${process.env.WHATSAPP_QICHAT_WEBHOOK}`,
        events: [
          'QRCODE_UPDATED',
          'MESSAGES_SET',
          'MESSAGES_UPSERT',
          'MESSAGES_UPDATE',
          'MESSAGES_DELETE',
          'SEND_MESSAGE',
          'CONNECTION_UPDATE',
          'REMOVE_INSTANCE',
        ],
      },
    }

    return this.axios
      .post(url, body)
      .then((response: AxiosResponse) => {
        return {
          error: false,
          status: response.status,
          data: response.data,
        }
      })
      .catch((error: any) => {
        console.log('ERROR', error)
        return {
          error: true,
          status: error?.response?.status ? error?.response?.status : '',
          message: error.message,
        }
      })
  }

  async fetchInstances(
    instanceId: string,
    accountId: string,
    jwt: string | null
  ): Promise<WhatsappServiceReponseType> {
    let result: WhatsappServiceReponseType

    const instanceName = makeInstanceName(accountId, instanceId)
    const url = `/instance/fetchInstances?instanceName=${instanceName}`

    console.log('🗝️ Fetching instance', {
      instanceName,
      jwt,
    })
    this.setHeadersJWT(jwt)
    try {
      const response: AxiosResponse = await this.axios.get(url)
      // console.log('🗝️ Fetching instance response', response)
      result = {
        error: false,
        status: response.status,
        data: response.data,
      }
    } catch (error: any) {
      result = {
        error: true,
        status: error?.response?.status ? error?.response?.status : '',
        message: error.message,
        data: {},
      }
    }
    return result
  }

  async status(
    instanceId: string,
    accountId: string,
    jwt: string | null
  ): Promise<WhatsappServiceReponseType> {
    const instanceName = makeInstanceName(accountId, instanceId)
    const url = `/instance/connectionState/${instanceName}`
    let result: WhatsappServiceReponseType

    this.setHeadersJWT(jwt)
    return this.axios
      .get(url, {
        headers: {
          Authorization: `Bearer ${jwt}`,
        },
      })
      .then((response: any) => {
        result = {
          error: false,
          status: response.status,
          data: response.data,
        }
        return result
      })
      .catch((error: any) => {
        result = {
          error: true,
          status: error?.response?.status || '',
          message: error.message,
          data: {},
        }
        return result
      })
  }

  async connect(
    instanceId: string,
    accountId: string,
    jwt: string | null
  ): Promise<WhatsappServiceReponseType> {
    const instanceName = makeInstanceName(accountId, instanceId)
    const url = `/instance/connect/${instanceName}`
    console.log('🗝️ Connecting instance', instanceName)
    let result: WhatsappServiceReponseType

    this.setHeadersJWT(jwt)

    return this.axios
      .get(url)
      .then((response: any) => {
        console.log('🗝️ Connecting instance response', response)
        result = {
          error: false,
          status: response.status,
          data: response.data,
        }
        return result
      })
      .catch((error: any) => {
        result = {
          error: true,
          status: error?.response?.status || '',
          message: error.message,
          data: {},
        }
        return result
      })
  }

  async disconnect(
    instanceId: string,
    accountId: string,
    jwt: string | null
  ): Promise<WhatsappServiceReponseType> {
    let result: WhatsappServiceReponseType

    const instanceName = makeInstanceName(accountId, instanceId)
    const url = `/instance/logout/${instanceName}`
    console.log('🗝️ Disconnecting instance evolution', instanceName)

    this.setHeadersJWT(jwt)

    try {
      const response: AxiosResponse = await this.axios.delete(url, {
        headers: {
          Authorization: `Bearer ${jwt}`,
        },
      })
      result = {
        error: false,
        status: response.status,
        data: response.data,
      }
    } catch (error: any) {
      result = {
        error: true,
        status: error?.response?.status ? error?.response?.status : '',
        message: error.message,
        data: {},
      }
    }
    return result
  }

  async delete(
    instanceId: string,
    accountId: string,
    jwt: string | null
  ): Promise<WhatsappServiceReponseType> {
    const instanceName = makeInstanceName(accountId, instanceId)
    console.log('🗝️ Deleting instance', instanceName)
    const url = `/instance/delete/${instanceName}`
    let result: WhatsappServiceReponseType

    this.setHeadersJWT(jwt)

    try {
      const response: AxiosResponse = await this.axios.delete(url, {
        headers: {
          Authorization: `Bearer ${jwt}`,
        },
      })
      result = {
        error: false,
        status: response.status,
        data: response.data,
      }
      console.log('🗝️ Instance deleted')
    } catch (error: any) {
      result = {
        error: true,
        status: error?.response?.status ? error?.response?.status : '',
        message: error.message,
        data: {},
      }
      console.log('🗝️ Instance not deleted', error)
    }
    return result
  }

  async sendText(
    instanceId: string,
    accountId: string,
    phone: string,
    message: string,
    linkPreview = true,
    delay?: number
  ): Promise<WhatsappServiceReponseType> {
    const instanceName = makeInstanceName(accountId, instanceId)
    const url = `/message/sendText/${instanceName}`
    let result: WhatsappServiceReponseType
    const body = {
      number: phone,
      text: message,
      linkPreview,
      delay,
    }

    try {
      const response: AxiosResponse = await this.axios.post(url, body)
      result = {
        error: false,
        status: response.status,
        data: response.data,
      }
    } catch (error: any) {
      result = {
        error: true,
        status: error?.response?.status ? error?.response?.status : '',
        message: error.message,
        data: {},
      }
    }
    return result
  }

  async sendAudio(
    instanceId: string,
    accountId: string,
    phone: string,
    audio: string
  ) {
    const instanceName = makeInstanceName(accountId, instanceId)
    const url = `/message/sendWhatsAppAudio/${instanceName}`
    const body = {
      number: phone,
      audio: audio,
    }
    try {
      const response: AxiosResponse = await this.axios.post(url, body)
      return response
    } catch (error: any) {
      return error
    }
  }
  async sendAudioFile(
    instanceId: string,
    accountId: string,
    phone: string,
    audio: any
  ) {
    const instanceName = makeInstanceName(accountId, instanceId)
    const url = `/message/sendWhatsAppAudioFile/${instanceName}`
    console.log(audio)
    const audioBlob = new Blob([audio.buffer])
    //const audioFile = fs.createReadStream(audio.buffer).toString()
    const formData = new FormData()

    formData.append('attachment', audioBlob, audio.originalname)
    formData.append('number', phone)
    try {
      return await this.axios
        .post(url, formData, {
          headers: { 'Content-Type': 'multipart/form-data' },
        })
        .then(async (sendResult: AxiosResponse) => {
          return await this.getBase64FromMediaMessage(
            instanceId,
            accountId,
            sendResult.data.key.id
          )
            .then((dataResult) => {
              return { sendResult, dataResult }
            })
            .catch((err: AxiosResponse) => {
              return
            })
        })
        .catch((err: AxiosResponse) => {
          console.error(err)
          return
        })
    } catch (error: any) {
      console.log(error.response.data)
      return error
    }
  }

  async sendPresence(
    instanceId: string,
    accountId: string,
    phone: string,
    presence: string,
    jwt: string
  ) {
    const instance = makeInstanceName(accountId, instanceId)
    const url = `/chat/sendPresence/${instance}`
    this.setHeadersJWT(jwt)
    let result: WhatsappServiceReponseType
    const body = {
      number: phone,
      presence,
      delay: 3000,
    }
    try {
      const response: AxiosResponse = await this.axios.post(url, body)
      result = {
        error: false,
        status: response.status,
        data: response.data,
      }
    } catch (error: any) {
      console.log('error', error)
      result = {
        error: true,
        status: error?.response?.status ? error?.response?.status : '',
        message: error.message,
        data: {},
      }
    }
    return result
  }

  async sendFile(
    instanceId: string,
    accountId: string,
    phone: string,
    mediaUrl: string,
    type: string,
    fileName: string,
    caption?: string,
    delay?: number
  ) {
    const instance = makeInstanceName(accountId, instanceId)
    const body = {
      number: phone,
      mediatype: type,
      caption: caption,
      media: mediaUrl,
      fileName,
    }

    if (delay) {
      await waitMiliseconds(delay)
    }

    return this.axios.post(`/message/sendMedia/${instance}`, body)
  }

  async markMessageAsRead(
    instanceId: string,
    accountId: string,
    readMessages: MessageReadType[]
  ): Promise<WhatsappServiceReponseType> {
    const instanceName = makeInstanceName(accountId, instanceId)
    const url = `/chat/markMessageAsRead/${instanceName}`
    let result: WhatsappServiceReponseType
    const body = { readMessages }

    try {
      const response: AxiosResponse = await this.axios.put(url, body)
      result = {
        error: false,
        status: response.status,
        data: response.data,
      }
    } catch (error: any) {
      result = {
        error: true,
        status: error?.response?.status ? error?.response?.status : '',
        message: error.message,
        data: {},
      }
    }
    return result
  }

  async retrieverMediaMessage(
    instanceId: string,
    accountId: string,
    messageId: string
  ) {
    const instanceName = makeInstanceName(accountId, instanceId)
    const url = `/chat/retrieverMediaMessage/${instanceName}`
    let result: WhatsappServiceReponseType
    const body = {
      keyId: messageId,
    }
    try {
      const response: AxiosResponse = await this.axios.post(url, body)
      console.log(response.headers)
      console.log(response.headers['content-type'])
      return response
    } catch (error: any) {
      result = {
        error: true,
        status: error?.response?.status ? error?.response?.status : '',
        message: error.message,
        data: {},
      }
    }

    return result
  }

  async getBase64FromMediaMessage(
    instanceId: string,
    accountId: string,
    messageId: string
  ) {
    const instanceName = makeInstanceName(accountId, instanceId)
    const url = `/chat/getBase64FromMediaMessage/${instanceName}`
    let result: WhatsappServiceReponseType
    const body = {
      key: {
        id: messageId,
      },
    }
    try {
      const response: AxiosResponse = await this.axios.post(url, body)
      result = {
        error: false,
        status: response.status,
        data: response.data,
      }
    } catch (error: any) {
      result = {
        error: true,
        status: error?.response?.status ? error?.response?.status : '',
        message: error.message,
        data: {},
      }
    }

    return result
  }

  async sniperCreate(
    instanceId: string,
    accountId: string,
    publicSniperId: string,
    enabled: boolean,
    jwt: string
  ) {
    const instanceName = makeInstanceName(accountId, instanceId)

    const url = `/typebot/create/${instanceName}`

    let result: WhatsappServiceReponseType
    const body = {
      enabled,
      typebot: publicSniperId,
      url: process.env.SNIPER_URL,
      listening_from_me: false,
      expire: 0,
      keyword_finish: '#SAIR',
      unknown_message: 'Desculpe, poderia repetir?',
      description: 'Sniper',
      triggerType: 'all',
      triggerOperator: 'contains',
      triggerValue: '',
    }
    this.setHeadersJWT(jwt)
    try {
      const response: AxiosResponse = await this.axios.post(url, body)
      result = {
        error: false,
        status: response.status,
        data: response.data,
      }
    } catch (error: any) {
      result = {
        error: true,
        status: error?.response?.status ? error?.response?.status : '',
        message: error.message,
        data: {},
      }
    }

    return result
  }

  async sniperUpdate(
    instanceId: string,
    accountId: string,
    publicSniperId: string,
    enabled: boolean,
    jwt: string,
    integrationId: string
  ) {
    const instanceName = makeInstanceName(accountId, instanceId)

    const url = `typebot/update/${integrationId}/${instanceName}`

    let result: WhatsappServiceReponseType
    const body = {
      enabled,
      typebot: publicSniperId,
      url: process.env.SNIPER_URL,
      listening_from_me: false,
      expire: 0,
      keyword_finish: '#SAIR',
      unknown_message: 'Desculpe, poderia repetir?',
      description: 'Sniper',
      triggerType: 'all',
      triggerOperator: 'contains',
      triggerValue: '',
    }
    this.setHeadersJWT(jwt)
    try {
      const response: AxiosResponse = await this.axios.put(url, body)
      result = {
        error: false,
        status: response.status,
        data: response.data,
      }
    } catch (error: any) {
      result = {
        error: true,
        status: error?.response?.status ? error?.response?.status : '',
        message: error.message,
        data: {},
      }
    }

    return result
  }

  async findIntegration(
    instanceId: string,
    accountId: string,
    jwt: string
  ): Promise<WhatsappServiceReponseType> {
    const instanceName = makeInstanceName(accountId, instanceId)
    const url = `/typebot/find/${instanceName}`
    let result: WhatsappServiceReponseType
    this.setHeadersJWT(jwt)

    try {
      const response: AxiosResponse = await this.axios.get(url)
      result = {
        error: false,
        status: response.status,
        data: response.data,
      }
    } catch (error: any) {
      result = {
        error: true,
        status: error?.response?.status ? error?.response?.status : '',
        message: error.message,
        data: {},
      }
    }

    return result
  }

  async getSession(
    instanceId: string,
    accountId: string,
    remoteJid: string,
    jwt: string
  ) {
    const instanceName = makeInstanceName(accountId, instanceId)
    const url = `/typebot/last_session/find/${instanceName}`
    let result: WhatsappServiceReponseType

    const headers = {
      headers: {
        Authorization: `Bearer ${jwt}`,
      },
    }

    const body = { remoteJid }

    try {
      const response: AxiosResponse = await this.axios.post(url, body, headers)
      result = {
        error: false,
        status: response.status,
        data: response.data,
      }
    } catch (error: any) {
      result = {
        error: true,
        status: error?.response?.status ? error?.response?.status : '',
        message: error.message,
        data: {},
      }
    }

    return result
  }

  async updateSession(
    instanceId: string,
    accountId: string,
    remoteJid: string,
    status: string,
    jwt: string
  ) {
    const instanceName = makeInstanceName(accountId, instanceId)
    const url = `/typebot/changeStatus/${instanceName}`
    let result: WhatsappServiceReponseType

    const headers = {
      headers: {
        Authorization: `Bearer ${jwt}`,
      },
    }

    const body = {
      remoteJid,
      status,
    }

    try {
      const response: AxiosResponse = await this.axios.post(url, body, headers)
      result = {
        error: false,
        status: response.status,
        data: response.data,
      }
    } catch (error: any) {
      result = {
        error: true,
        status: error?.response?.status ? error?.response?.status : '',
        message: error.message,
        data: {},
      }
    }

    return result
  }

  async whatsappNumbers(
    instanceId: string,
    accountId: string,
    phone: string,
    jwt: string
  ): Promise<WhatsappServiceReponseType> {
    const instanceName = makeInstanceName(accountId, instanceId)
    const url = `/chat/whatsappNumbers/${instanceName}`
    let result: WhatsappServiceReponseType

    this.setHeadersJWT(jwt)
    const body = {
      numbers: [phone],
    }

    try {
      const response: AxiosResponse = await this.axios.post(url, body)
      result = {
        error: false,
        status: response.status,
        data: response.data[0],
      }
    } catch (error: any) {
      result = {
        error: true,
        status: error?.response?.status ? error?.response?.status : '',
        message: error.message,
        data: {},
      }
    }

    return result
  }

  async webhook(
    instanceId: string,
    accountId: string
  ): Promise<WhatsappServiceReponseType> {
    const instanceName = makeInstanceName(accountId, instanceId)
    const url = `/webhook/set/${instanceName}`
    let result: WhatsappServiceReponseType
    const body = {
      enabled: true,
      url: `${process.env.WHATSAPP_QICHAT_WEBHOOK}`,
    }
    try {
      const response: AxiosResponse = await this.axios.post(url, body)
      result = {
        error: false,
        status: response.status,
        data: response.data,
      }
    } catch (error: any) {
      result = {
        error: true,
        status: error?.response?.status ? error?.response?.status : '',
        message: error.message,
        data: {},
      }
    }
    return result
  }

  // Group Management Methods
  async createGroup(
    instanceId: string,
    accountId: string,
    groupName: string,
    description: string = '',
    members: string[]
  ): Promise<WhatsappServiceReponseType<CreateGroupResponse>> {
    // Use mock service in development or when Evolution API is not available
    if (NODE_ENV === 'development' || !WHATSAPP_API_URL) {
      console.log('🔧 Using mock group service for development')
      return await mockGroupService.createGroup(instanceId, accountId, groupName, description, members)
    }

    const instanceName = makeInstanceName(accountId, instanceId)
    const url = `/group/create/${instanceName}`
    let result: WhatsappServiceReponseType<CreateGroupResponse>

    const body = {
      subject: groupName,
      description,
      participants: members,
    }

    try {
      const response: AxiosResponse = await this.axios.post(url, body)
      result = {
        error: false,
        status: response.status,
        data: {
          success: true,
          groupId: response.data?.groupId,
          groupJid: response.data?.groupJid,
          message: 'Group created successfully',
        },
      }
    } catch (error: any) {
      result = {
        error: true,
        status: error?.response?.status ? error?.response?.status : '',
        message: error.message,
        data: {
          success: false,
          error: error.message,
          message: 'Failed to create group',
        },
      }
    }
    return result
  }

  async addGroupParticipants(
    instanceId: string,
    accountId: string,
    groupJid: string,
    participants: string[]
  ): Promise<WhatsappServiceReponseType> {
    // Use mock service in development or when Evolution API is not available
    if (NODE_ENV === 'development' || !WHATSAPP_API_URL) {
      console.log('🔧 Using mock group service for adding participants')
      return await mockGroupService.addGroupParticipants(instanceId, accountId, groupJid, participants)
    }

    const instanceName = makeInstanceName(accountId, instanceId)
    const url = `/group/participants/add/${instanceName}`
    let result: WhatsappServiceReponseType

    const body = {
      groupJid,
      participants,
    }

    try {
      const response: AxiosResponse = await this.axios.post(url, body)
      result = {
        error: false,
        status: response.status,
        data: response.data,
      }
    } catch (error: any) {
      result = {
        error: true,
        status: error?.response?.status ? error?.response?.status : '',
        message: error.message,
        data: {},
      }
    }
    return result
  }

  async getGroupInfo(
    instanceId: string,
    accountId: string,
    groupJid: string
  ): Promise<WhatsappServiceReponseType> {
    // Use mock service in development or when Evolution API is not available
    if (NODE_ENV === 'development' || !WHATSAPP_API_URL) {
      console.log('🔧 Using mock group service for group info')
      return await mockGroupService.getGroupInfo(instanceId, accountId, groupJid)
    }

    const instanceName = makeInstanceName(accountId, instanceId)
    const url = `/group/info/${instanceName}?groupJid=${groupJid}`
    let result: WhatsappServiceReponseType

    try {
      const response: AxiosResponse = await this.axios.get(url)
      result = {
        error: false,
        status: response.status,
        data: response.data,
      }
    } catch (error: any) {
      result = {
        error: true,
        status: error?.response?.status ? error?.response?.status : '',
        message: error.message,
        data: {},
      }
    }
    return result
  }

  async listGroups(
    instanceId: string,
    accountId: string
  ): Promise<WhatsappServiceReponseType> {
    // Use mock service in development or when Evolution API is not available
    if (NODE_ENV === 'development' || !WHATSAPP_API_URL) {
      console.log('🔧 Using mock group service for listing groups')
      return await mockGroupService.listGroups(instanceId, accountId)
    }

    const instanceName = makeInstanceName(accountId, instanceId)
    const url = `/group/list/${instanceName}`
    let result: WhatsappServiceReponseType

    try {
      const response: AxiosResponse = await this.axios.get(url)
      result = {
        error: false,
        status: response.status,
        data: response.data,
      }
    } catch (error: any) {
      result = {
        error: true,
        status: error?.response?.status ? error?.response?.status : '',
        message: error.message,
        data: {},
      }
    }
    return result
  }
}

export const EvolutionServiceType = typeof EvolutionService
