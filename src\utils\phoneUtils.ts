/**
 * Phone number utilities for WhatsApp group management
 */

/**
 * Normalize phone number to WhatsApp format
 * @param phone - Phone number in various formats
 * @returns Normalized phone number or null if invalid
 */
export function normalizePhoneNumber(phone: string): string | null {
  if (!phone || typeof phone !== 'string') {
    return null
  }

  // Remove all non-digit characters
  let cleanPhone = phone.replace(/\D/g, '')

  // Remove leading zeros
  cleanPhone = cleanPhone.replace(/^0+/, '')

  // If phone starts with country code, keep it
  // If it doesn't, assume it's a Brazilian number and add 55
  if (cleanPhone.length === 11 && cleanPhone.startsWith('1')) {
    // Brazilian mobile number without country code (11 digits starting with 1)
    cleanPhone = '55' + cleanPhone
  } else if (cleanPhone.length === 10 && !cleanPhone.startsWith('55')) {
    // Brazilian landline without country code (10 digits)
    cleanPhone = '55' + cleanPhone
  } else if (cleanPhone.length === 9 && !cleanPhone.startsWith('55')) {
    // Brazilian mobile without area code and country code
    // This is ambiguous, so we'll reject it
    return null
  }

  // Validate final length (should be between 10-15 digits for international format)
  if (cleanPhone.length < 10 || cleanPhone.length > 15) {
    return null
  }

  return cleanPhone
}

/**
 * Validate if phone number is in correct format for WhatsApp
 * @param phone - Phone number to validate
 * @returns true if valid, false otherwise
 */
export function isValidWhatsAppPhone(phone: string): boolean {
  const normalized = normalizePhoneNumber(phone)
  return normalized !== null
}

/**
 * Format phone number for display
 * @param phone - Phone number to format
 * @returns Formatted phone number
 */
export function formatPhoneForDisplay(phone: string): string {
  const normalized = normalizePhoneNumber(phone)
  if (!normalized) return phone

  // Format Brazilian numbers
  if (normalized.startsWith('55')) {
    const number = normalized.substring(2)
    if (number.length === 11) {
      // Mobile: +55 (11) 99999-9999
      return `+55 (${number.substring(0, 2)}) ${number.substring(2, 7)}-${number.substring(7)}`
    } else if (number.length === 10) {
      // Landline: +55 (11) 9999-9999
      return `+55 (${number.substring(0, 2)}) ${number.substring(2, 6)}-${number.substring(6)}`
    }
  }

  // For other countries, just add + prefix
  return `+${normalized}`
}

/**
 * Normalize array of phone numbers
 * @param phones - Array of phone numbers
 * @returns Object with valid and invalid phone numbers
 */
export function normalizePhoneNumbers(phones: string[]): {
  valid: string[]
  invalid: string[]
  normalized: string[]
} {
  const valid: string[] = []
  const invalid: string[] = []
  const normalized: string[] = []

  phones.forEach(phone => {
    const norm = normalizePhoneNumber(phone)
    if (norm) {
      valid.push(phone)
      normalized.push(norm)
    } else {
      invalid.push(phone)
    }
  })

  return { valid, invalid, normalized }
}

/**
 * Convert phone number to WhatsApp JID format
 * @param phone - Phone number
 * @returns WhatsApp JID (e.g., "<EMAIL>")
 */
export function phoneToWhatsAppJid(phone: string): string | null {
  const normalized = normalizePhoneNumber(phone)
  if (!normalized) return null
  
  return `${normalized}@s.whatsapp.net`
}

/**
 * Extract phone number from WhatsApp JID
 * @param jid - WhatsApp JID
 * @returns Phone number or null if invalid
 */
export function whatsAppJidToPhone(jid: string): string | null {
  if (!jid || typeof jid !== 'string') return null
  
  const match = jid.match(/^(\d+)@s\.whatsapp\.net$/)
  return match ? match[1] : null
}

/**
 * Validate and prepare phone numbers for Evolution API
 * @param phones - Array of phone numbers
 * @returns Prepared phone numbers and validation results
 */
export function preparePhoneNumbersForAPI(phones: string[]): {
  success: boolean
  prepared: string[]
  errors: string[]
  summary: {
    total: number
    valid: number
    invalid: number
  }
} {
  if (!Array.isArray(phones)) {
    return {
      success: false,
      prepared: [],
      errors: ['Input must be an array of phone numbers'],
      summary: { total: 0, valid: 0, invalid: 0 }
    }
  }

  const { valid, invalid, normalized } = normalizePhoneNumbers(phones)
  const errors: string[] = []

  if (invalid.length > 0) {
    errors.push(`Invalid phone numbers: ${invalid.join(', ')}`)
  }

  if (normalized.length === 0) {
    errors.push('No valid phone numbers provided')
  }

  return {
    success: normalized.length > 0,
    prepared: normalized,
    errors,
    summary: {
      total: phones.length,
      valid: normalized.length,
      invalid: invalid.length
    }
  }
}

/**
 * Common phone number formats for testing
 */
export const PHONE_EXAMPLES = {
  BRAZILIAN_MOBILE: '5511999999999',
  BRAZILIAN_LANDLINE: '5511999999999',
  US_MOBILE: '15551234567',
  FORMATTED_BRAZILIAN: '+55 (11) 99999-9999',
  WITH_SPACES: '55 11 99999 9999',
  WITH_DASHES: '55-11-99999-9999',
  WITH_PARENTHESES: '55 (11) 99999-9999'
}

export default {
  normalizePhoneNumber,
  isValidWhatsAppPhone,
  formatPhoneForDisplay,
  normalizePhoneNumbers,
  phoneToWhatsAppJid,
  whatsAppJidToPhone,
  preparePhoneNumbersForAPI,
  PHONE_EXAMPLES
}
