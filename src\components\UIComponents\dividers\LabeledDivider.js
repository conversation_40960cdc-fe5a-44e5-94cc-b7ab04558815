/**
 * List Dividers
 */
import React from 'react'

const LabeledDivider = ({ label, pos, hrClasses, customClasses, first, labelrops, labeClasses, onToggle, isToggled, ...props }) => (
  <div
    className={`LabeledDividerComponent labeled-divider label-to-${pos || 'center'} ${first ? 'first' : ''} ${onToggle ? 'pointer' : ''} ${customClasses || ''}`}
    onClick={e => !!onToggle && onToggle()}
  >
    <hr className={hrClasses || ''} />
    <span className={`label ${labeClasses || ''}`} {...labelrops}>
      {label || ''}
    </span>
    {!!onToggle && <i className={`fa toggler text-white fa-chevron-${isToggled ? 'down' : 'up'}`} />}
  </div>
)
export default LabeledDivider
