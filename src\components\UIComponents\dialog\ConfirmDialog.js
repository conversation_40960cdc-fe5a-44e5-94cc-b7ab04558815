/**
 * Simple Example
 */
import React from 'react'
import { Button, <PERSON>alog, DialogActions, DialogContent, DialogContentText, DialogTitle } from '@material-ui/core'
import { langMessages } from '../../../lang'

const DialogBtn = ({ cancel, ...props }) => {
  let { text, icon, color } = props || {}

  return (
    <Button variant="contained" className={`btn-${color || (cancel ? 'danger' : 'success')} text-white`} {...props} color="default">
      {(icon && <i className={`${icon} mr-5`}></i>) || null}
      {text || langMessages[`button.${cancel ? 'cancel' : 'confirm'}`]}
    </Button>
  )
}

const ConfirmDialog = props => {
  const { cancelBtnProps, okBtnProps } = props
  return (
    <Dialog open onClose={() => this.handleClose()} aria-labelledby="alert-dialog-title" aria-describedby="alert-dialog-description">
      <DialogTitle id="alert-dialog-title">{props.title || ''}</DialogTitle>
      <DialogContent>
        <DialogContentText id="alert-dialog-description">{props.description || ''}</DialogContentText>
      </DialogContent>
      <DialogActions>
        <DialogBtn
          cancel
          onClick={() => {
            ;(props.onCancel && props.onCancel()) || props.onConfirm(false)
          }}
          {...cancelBtnProps}
        />
        <DialogBtn
          onClick={() => {
            props.onConfirm(true)
          }}
          {...okBtnProps}
        />
      </DialogActions>
    </Dialog>
  )
}

export default ConfirmDialog
