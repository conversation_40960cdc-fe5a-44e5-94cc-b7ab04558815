import { Box } from '@material-ui/core'
import { langMessages } from 'Lang/index'
import React from 'react'
import { Chart } from 'react-google-charts'

export default function GoogleChart(props) {
  const { darkMode, options, data, chartType = 'BarChart' } = props

  const textStyle = {
    color: darkMode ? 'white' : 'black',
  }
  const defaultOptions = {
    backgroundColor: 'transparent',
    chartArea: {
      backgroundColor: 'transparent',
    },
    titleTextStyle: {
      color: textStyle.color,
    },
    subTitleTextStyle: {
      color: textStyle.color,
    },
    legend: {
      titleTextStyle: {
        color: textStyle.color,
      },
    },
    hAxis: {
      textStyle,
      ticks: data?.hTicks || [0, 10, 20, 30, 40, 50, 60, 70, 80, 90, 100],
    },
    vAxis: {
      ticks: data?.vTicks || [0, 10, 20, 30, 40, 50, 60, 70, 80, 90, 100],
    },
    ...options,
  }

  if (data.length === 1)
    return (
      <Box flex={1} display='flex' justifyContent='center'>
        <h4>{langMessages['reports.noData']}</h4>
      </Box>
    )

  return <Chart chartType={chartType} width='100%' height='500px' data={data} loader={<div>Loading Chart</div>} options={defaultOptions} />
}
