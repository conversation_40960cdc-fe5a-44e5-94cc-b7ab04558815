import React from 'react'

import { <PERSON> } from 'react-router-dom'
import { Alert } from 'reactstrap/lib'

import { langMessages } from '../../../lang'

const SettingsAlert = ({ investedValue, ...props }) => {
  const {
    account: { settings },
  } = props

  const hasInvestments = investedValue !== null || !!((settings || {}).investments || []).length
  const hasGoals = !!((settings || {}).goals || []).length

  const heading = !hasInvestments ? langMessages['placeholders.noInvestmentsFound'] : langMessages['placeholders.noGoalsFound']

  return !hasInvestments || !hasGoals ? (
    <div className="my-15">
      <Alert fade={false} color="gray" className="py-20 my-10">
        <div className="d-flex align-items-center">
          <div className="flex-0">
            <i className="fa fa-cog fa-3x text-white mr-15 op-2"></i>
          </div>
          <div className="flex-1">
            {!!heading && (
              <h4>
                {heading.replace('[%s]', langMessages['accounts.settings'])}
                <hr className="border-light-gray my-10 my-5 op-3" />
              </h4>
            )}
            {langMessages['placeholders.updateSettings'].replace('[%s]', langMessages['modules.accounts.singular'])} &nbsp;
            <Link to="/settings">
              <strong>{langMessages['texts.clickHereToSetup']}</strong>
            </Link>
          </div>
        </div>
      </Alert>
    </div>
  ) : null
}

export default SettingsAlert
