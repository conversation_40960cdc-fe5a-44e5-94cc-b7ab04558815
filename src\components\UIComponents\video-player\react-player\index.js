/* 
ReactPlayer
https://github.com/CookPete/react-player

[prop]				[default]	[description]
-------------------------------------------------------------------------------------------------------------------
url					object		The url of a video or song to play ◦  Can be an array or MediaStream object	
playing				false		Set to true or false to pause or play the media	false
loop				false		Set to true or false to loop the media	false
controls			false		Set to true or false to display native player controls. ◦  For Vimeo videos, hiding controls must be enabled by the video owner.	false
light				false		Set to true to show just the video thumbnail, which loads the full player on click ◦  Pass in an image URL to override the preview image	false
volume				null		Set the volume of the player, between 0 and 1 ◦  null uses default volume on all players #357	null
muted				false		Mutes the player ◦  Only works if volume is set	false
playbackRate		1			Set the playback rate of the player ◦  Only supported by YouTube, Wistia, and file paths	1
width				640px		Set the width of the player	640px
height				360px		Set the height of the player	360px
style				{}			Add inline styles to the root element	{}
progressInterval	1000		The time between onProgress callbacks, in milliseconds	1000
playsinline			false		Applies the playsinline attribute where supported	false
pip					false		Set to true or false to enable or disable picture-in-picture mode ◦  Only available when playing file URLs in certain browsers	false
stopOnUnmount		true		If you are using pip you may want to use stopOnUnmount={false} to continue playing in picture-in-picture mode even after ReactPlayer unmounts	true
fallback			null		Element or component to use as a fallback if you are using lazy loading	null
wrapper				div			Element or component to use as the container element	div
playIcon						Element or component to use as the play icon in light mode	
previewTabIndex		0			Set the tab index to be used on light mode	0
*/

import React, { Component } from 'react'

// Lazy load Player
import ReactPlayer from 'react-player'
import ReactPlayerLazy from 'react-player/lazy'

// intl messages
import IntlMessages from 'Util/IntlMessages'

// rct card box
import RctCollapsibleCard from 'Components/RctCollapsibleCard/RctCollapsibleCard'

const Shortcuts = []

class VideoPlayer extends Component {
  state = {
    displayShortcuts: false,
  }

  renderPlayer() {
    const PlayerComponent = this.props.lazy ? ReactPlayerLazy : ReactPlayer

    return (
      <PlayerComponent
        url={this.props.url || this.props.src || ''}
        playing={!!this.props.playing}
        loop={!!this.props.loop}
        controls={'controls' in this.props ? !!this.props.controls : true}
        light={['light', 'poster'].find(prop => prop in this.props) ? this.props.poster || this.props.light : true}
        volume={this.props.volume || 0.7}
        muted={!!this.props.muted}
        playbackRate={this.props.playbackRate || 1}
        width={this.props.width || null}
        height={this.props.height || null}
        style={this.props.style || null}
        progressInterval={this.props.progressInterval || null}
        playsinline={!!this.props.playsinline}
        pip={!!this.props.pip}
        stopOnUnmount={!!this.props.stopOnUnmount}
        // wrapper={this.props.wrapper||<div className="PlayerComponent PlayerComponent-wrapper w-100"/>} // ERROR
        fallback={this.props.fallback || null}
        playIcon={this.props.playIcon || <i className="fa fa-play fa-5x" />}
        previewTabIndex={this.props.previewTabIndex || 0}
      />
    )
  }

  render() {
    return (
      <div className="video-player-wrapper">
        {this.props.unwrap === false ? (
          <RctCollapsibleCard colClasses={this.props.colClasses || ''} heading={this.props.heading || ''}>
            {this.renderPlayer()}
          </RctCollapsibleCard>
        ) : (
          this.renderPlayer()
        )}
        {this.state.displayShortcuts && !!Shortcuts.length && (
          <RctCollapsibleCard colClasses="col-sm-12 col-md-12 col-lg-12" heading={<IntlMessages id="widgets.keyboardShortcuts" />}>
            <div className="table-responsive">
              <table className="table table-hover mb-0">
                <thead>
                  <tr>
                    <th>Action</th>
                    <th>Shortcut</th>
                    <th>Action</th>
                    <th>Shortcut</th>
                  </tr>
                </thead>
                <tbody>
                  {Shortcuts.map((data, key) => (
                    <tr key={key}>
                      <td>{data.action1}</td>
                      <td>{data.shortcut1}</td>
                      <td>{data.action2}</td>
                      <td>{data.shortcut2}</td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </RctCollapsibleCard>
        )}
      </div>
    )
  }
}
export default VideoPlayer
