import React from 'react'

import COLLECTIONS, { COLLECTION_TRIGGERS, QIUSERS_COLLECTION_NAME, TEAMS_COLLECTION_NAME } from '../../../constants/AppCollections'
import CONSTANTS from '../../../constants/AppConstants'

// lang strings
import { langMessages } from '../../../lang'

import AppModules from '../../../constants/AppModules'

import { RgbColors } from '../../../constants/ThemeColors'

import { dataAccumulators, dataCounters, requestModel } from '../helpers'
import { currencyFormat } from '../../../helpers/helpers'
import { findGoal, getGoalValue } from '../../../models/fields/goals'
import { fetchPostsFromRefs } from '../../../firebase/functions'
import ROLES from '../../../constants/UsersRoles'

const { APP_TRIGGERS } = CONSTANTS

export const dateFormats = [CONSTANTS.MOMENT_ISO_DAY, CONSTANTS.MOMENT_ISO_MONTH]
export const labelFormats = [CONSTANTS.MOMENT_SHORT_DAY, CONSTANTS.MOMENT_SHORT_MONTH]

export const dateFormat = dateFormats[0]
export const labelFormat = labelFormats[0]

export const collections = [
  COLLECTIONS.TICKETS_COLLECTION_NAME,
  COLLECTIONS.FUNNELS_COLLECTION_NAME,
  COLLECTIONS.DEALS_COLLECTION_NAME,
]

export const queryTriggers = {
  [COLLECTIONS.DEALS_COLLECTION_NAME]: [
    COLLECTION_TRIGGERS[COLLECTIONS.DEALS_COLLECTION_NAME].added,
    APP_TRIGGERS.APP_TRIGGER_WON,
    APP_TRIGGERS.APP_TRIGGER_LOST,
  ],
  [COLLECTIONS.FUNNELS_COLLECTION_NAME]: [APP_TRIGGERS.APP_TRIGGER_ADDED_TO_FUNNEL],
  [COLLECTIONS.TICKETS_COLLECTION_NAME]: [COLLECTION_TRIGGERS[COLLECTIONS.TICKETS_COLLECTION_NAME].added],
}

export const areaChartTriggers = {
  [COLLECTIONS.DEALS_COLLECTION_NAME]: [
    COLLECTION_TRIGGERS[COLLECTIONS.DEALS_COLLECTION_NAME].added,
    APP_TRIGGERS.APP_TRIGGER_WON,
    APP_TRIGGERS.APP_TRIGGER_LOST,
  ],
  [COLLECTIONS.FUNNELS_COLLECTION_NAME]: APP_TRIGGERS.APP_TRIGGER_ADDED_TO_FUNNEL,
  [COLLECTIONS.TICKETS_COLLECTION_NAME]: COLLECTION_TRIGGERS[COLLECTIONS.TICKETS_COLLECTION_NAME].added,
}

export const areaChartLabels = {
  [COLLECTIONS.DEALS_COLLECTION_NAME]: {
    [COLLECTION_TRIGGERS[COLLECTIONS.DEALS_COLLECTION_NAME].added]: langMessages['triggers.addedToFunnel'],
    [APP_TRIGGERS.APP_TRIGGER_WON]: langMessages['triggers.movedToWon'],
    [APP_TRIGGERS.APP_TRIGGER_LOST]: langMessages['triggers.movedToLost'],
  },
  [COLLECTIONS.FUNNELS_COLLECTION_NAME]: langMessages['stats.funnelsConvertions'],
  [COLLECTIONS.TICKETS_COLLECTION_NAME]: langMessages['stats.ticketsAddedCount'],
}

export const areaChartColors = {}
collections.forEach((collection, i) => {
  areaChartColors[collection] = AppModules[collection].color
})
areaChartColors[COLLECTIONS.DEALS_COLLECTION_NAME] = {
  [COLLECTION_TRIGGERS[COLLECTIONS.DEALS_COLLECTION_NAME].added]: RgbColors.primary,
  [APP_TRIGGERS.APP_TRIGGER_WON]: RgbColors.info,
  [APP_TRIGGERS.APP_TRIGGER_LOST]: RgbColors.danger,
}

export const prepareQueries = (props, state) => {
  let { funnelsIds } = state
  let { filters, account } = props

  let request = window.jsonClone({
    ...requestModel,
    dateFormats,
    filters,
  })

  let requests = collections.map(collection => {
    let collectionRequest = {
      ...window.jsonClone(request),
      collection,
      queries: [],
      groupQueries: [],
      postsQueries: [],
      counters: dataCounters[collection] || [],
      accumulators: dataAccumulators[collection] || [],
    }

    switch (collection) {
      case COLLECTIONS.FUNNELS_COLLECTION_NAME:
        if (funnelsIds.length) {
          collectionRequest.postsIds = funnelsIds
        }
        break
      case COLLECTIONS.DEALS_COLLECTION_NAME:
        if (funnelsIds.length) {
          collectionRequest.postsQueries = [[CONSTANTS.FUNNEL_ID_FIELD, 'in', funnelsIds]]
        }
        collectionRequest.queries.push(['trigger', 'in', queryTriggers[collection]])
        break
      case COLLECTIONS.TICKETS_COLLECTION_NAME:
        if (funnelsIds.length) {
          collectionRequest.postsQueries = [[`context.${CONSTANTS.FUNNEL_ID_FIELD}`, 'in', funnelsIds]]
        }
        collectionRequest.queries.push(['trigger', 'in', queryTriggers[collection]])
        break
      default:
        break
    }

    if (!collectionRequest.id && !['postsIds', 'queries', 'groupQueries', 'postsQueries'].find(k => !!collectionRequest[k].length)) {
      return null
    }

    return collectionRequest
  })

  return requests.filter(r => !!r)
}

export const compileTeamStats = ({ post, postId, team, collection, filters, goals, goalKey, relatedField, logsFunc, setState }) => {
  let where = []

  filters.start && where.push([CONSTANTS.CREATED_FIELD, '>=', filters.start])
  filters.end && where.push([CONSTANTS.CREATED_FIELD, '<=', filters.end])

  let queries = window.jsonClone(where)
  let keywords = []

  collections.forEach(col => {
    if (queryTriggers[col]) {
      queryTriggers[col].forEach(trigger => {
        keywords.push(`[${relatedField}]:[${postId}];[collection]:[${col}];[trigger]:[${trigger}]`)
      })
    } else {
      keywords.push(`[${relatedField}]:[${postId}];[collection]:[${col}]`)
    }
  })

  if (keywords.length) {
    queries.push([CONSTANTS.KEYWORDS_FIELD, 'array-contains-any', keywords])
  }

  const uniqueLogFields = ['trigger', 'collection', 'id']
  const filterUniqueLogs = (log, i, logs) => {
    let index = logs.findIndex(l => l.logId === log.logId)
    return !logs.find((l, n) => n < index && uniqueLogFields.filter(k => l[k] === log[k]).length === uniqueLogFields.length)
  }

  const contactFields = ['contactId', 'collection']
  const filterContactLogs = (log, i, logs) =>
    !logs.find((l, n) => n > i && contactFields.filter(k => l[k] === log[k]).length === contactFields.length)

  logsFunc(postId, collection, queries)
    .then(logs => {
      console.log(postId, collection, 'logs', logs)

      let addedLogs = logs.filter(filterUniqueLogs).filter(log => log.data && log.trigger === COLLECTION_TRIGGERS.deals.added)

      let addedCount = addedLogs.length
      let addedValue = addedLogs.reduce((t, log) => t + parseFloat(log.data.value || 0), 0)

      let addedValueAvg = addedCount ? Number(addedValue / addedCount).toFixed(2) : 0
      let addedGoal = findGoal(goals, goalKey, 'dealsAddedValue', postId, team)
      let addedGoalValue = getGoalValue(addedGoal, filters.start, filters.end)
      let addedGoalRate = addedGoalValue ? ((addedValue / addedGoalValue) * 100).toFixed(2) : 0
      let addedCountGoal = findGoal(goals, goalKey, 'dealsAdded', postId, team)
      let addedCountGoalValue = getGoalValue(addedCountGoal, filters.start, filters.end)
      let addedCountGoalRate = addedCountGoalValue ? ((addedCount / addedCountGoalValue) * 100).toFixed(2) : 0

      let wonLogs = logs
        .filter(filterUniqueLogs)
        .filter(log => log.data && log.trigger === APP_TRIGGERS.APP_TRIGGER_WON && log.collection === COLLECTIONS.DEALS_COLLECTION_NAME)

      let wonCount = wonLogs.length
      let wonValue = wonLogs.reduce((t, log) => t + parseFloat(log.data.value || 0), 0)

      let wonValueAvg = wonCount ? Number(wonValue / wonCount).toFixed(2) : 0
      let wonGoal = findGoal(goals, goalKey, 'dealsWonValue', postId, team)
      let wonGoalValue = getGoalValue(wonGoal, filters.start, filters.end)
      let wonGoalRate = wonGoalValue ? ((wonValue / wonGoalValue) * 100).toFixed(2) : 0
      let wonCountGoal = findGoal(goals, goalKey, 'dealsWon', postId, team)
      let wonCountGoalValue = getGoalValue(wonCountGoal, filters.start, filters.end)
      let wonCountGoalRate = wonCountGoalValue ? ((wonCount / wonCountGoalValue) * 100).toFixed(2) : 0

      let lostLogs = logs
        .filter(filterUniqueLogs)
        .filter(log => log.data && log.trigger === APP_TRIGGERS.APP_TRIGGER_LOST && log.collection === COLLECTIONS.DEALS_COLLECTION_NAME)

      let lostCount = lostLogs.length
      let lostValue = lostLogs.reduce((t, log) => t + parseFloat(log.data.value || 0), 0)

      let lostValueAvg = lostCount ? Number(lostValue / lostCount).toFixed(2) : 0
      let lostGoal = findGoal(goals, goalKey, 'dealsLostValue', postId, team)
      let lostGoalValue = getGoalValue(lostGoal, filters.start, filters.end)
      let lostGoalRate = lostGoalValue ? ((lostValue / lostGoalValue) * 100).toFixed(2) : 0
      let lostCountGoal = findGoal(goals, goalKey, 'dealsLost', postId, team)
      let lostCountGoalValue = getGoalValue(lostCountGoal, filters.start, filters.end)
      let lostCountGoalRate = lostCountGoalValue ? ((lostCount / lostCountGoalValue) * 100).toFixed(2) : 0

      let contactsLogs = logs.filter(filterContactLogs).filter(log => log.contactId && log.collection === COLLECTIONS.DEALS_COLLECTION_NAME)

      let totalCount = contactsLogs.length

      let activity = [
        { label: langMessages['menu.contacts'], value: totalCount },
        { label: langMessages['stats.generated'], value: addedCount },
        { label: langMessages['stats.won'], value: wonCount },
        { label: langMessages['stats.lost'], value: lostCount },
      ]

      let head = [
        {
          type: 'head',
          columns: [
            {
              key: 'label',
              label: langMessages['modules.deals'],
              align: 'left',
              rowSpan: 2,
              className: 'fw-bold font-lg',
            },
            {
              key: 'goal-values',
              label: `${langMessages['texts.value']}`,
              colSpan: 3,
              className: 'bg-gray text-center',
            },
            {
              key: 'goal-qty',
              label: `${langMessages['texts.count']}`,
              colSpan: 3,
              className: 'bg-gray text-center',
            },
          ],
        },
        {
          type: 'head',
          columns: [
            {
              key: 'value',
              label: `${langMessages['widgets.total']}`,
              className: 'border dark-theme-border-darker text-center',
            },
            {
              key: 'goal',
              label: `${langMessages['texts.goal']}`,
              className: 'border dark-theme-border-darker text-center',
            },
            {
              key: 'result',
              label: `${langMessages['texts.result']}`,
              className: 'border dark-theme-border-darker text-center',
            },
            {
              key: 'count',
              label: `${langMessages['widgets.total']}`,
              className: 'border dark-theme-border-darker text-center',
            },
            {
              key: 'countGoal',
              label: `${langMessages['texts.goal']}`,
              className: 'border dark-theme-border-darker text-center',
            },
            {
              key: 'countResult',
              label: `${langMessages['texts.result']}`,
              className: 'border dark-theme-border-darker text-center',
            },
          ],
        },
      ]

      let deals = [
        ...head,
        {
          color: 'primary',
          icon: 'zmdi zmdi-star',
          label: langMessages['widgets.new.plural'],
          sublabel: `${langMessages['stats.valueAverage']} R$${addedValueAvg}`,
          value: `R$ ${currencyFormat(addedValue)}`,
          goal: `R$ ${currencyFormat(addedGoalValue)}`,
          result: <span className={parseFloat(addedGoalRate) >= 100 ? 'text-success' : 'text-danger'}>{`${addedGoalRate}%`}</span>,
          count: addedCount,
          countGoal: addedCountGoalValue.toFixed(0),
          countResult: <span className={parseFloat(addedCountGoalRate) >= 100 ? 'text-success' : 'text-danger'}>{`${addedCountGoalRate}%`}</span>,
        },
        {
          color: 'info',
          icon: 'fa fa-thumbs-up',
          label: langMessages['stats.won'],
          sublabel: `${langMessages['stats.valueAverage']} R$${wonValueAvg}`,
          value: `R$ ${currencyFormat(wonValue)}`,
          goal: `R$ ${currencyFormat(wonGoalValue)}`,
          result: <span className={parseFloat(wonGoalRate) >= 100 ? 'text-success' : 'text-danger'}>{`${wonGoalRate}%`}</span>,
          count: wonCount,
          countGoal: wonCountGoalValue.toFixed(0),
          countResult: <span className={parseFloat(wonCountGoalRate) >= 100 ? 'text-success' : 'text-danger'}>{`${wonCountGoalRate}%`}</span>,
        },
        {
          color: 'danger',
          icon: 'fa fa-thumbs-down',
          label: langMessages['stats.lost'],
          sublabel: `${langMessages['stats.valueAverage']} R$${lostValueAvg}`,
          value: `R$ ${currencyFormat(lostValue)}`,
          goal: `R$ ${currencyFormat(lostGoalValue)}`,
          result: <span className={parseFloat(lostGoalRate) < 100 ? 'text-success' : 'text-danger'}>{`${lostGoalRate}%`}</span>,
          count: lostCount,
          countGoal: lostCountGoalValue.toFixed(0),
          countResult: <span className={parseFloat(lostCountGoalRate) < 100 ? 'text-success' : 'text-danger'}>{`${lostCountGoalRate}%`}</span>,
        },
      ]

      let conversionLogs = logs.filter(filterUniqueLogs).filter(log => log.data && log.trigger === COLLECTION_TRIGGERS.tickets.added)

      let conversionCount = conversionLogs.length
      let conversionTotal = conversionLogs.reduce((t, log) => t + parseFloat(log.data.total || 0), 0)

      let conversionTotalAvg = conversionCount ? Number(conversionTotal / conversionCount).toFixed(2) : 0
      let conversionGoal = findGoal(goals, goalKey, 'ticketsTotal', postId, team)
      let conversionGoalValue = getGoalValue(conversionGoal, filters.start, filters.end)
      let conversionGoalRate = conversionGoalValue ? ((conversionTotal / conversionGoalValue) * 100).toFixed(2) : 0
      let conversionCountGoal = findGoal(goals, goalKey, 'conversionsCount', postId, team)
      let conversionCountGoalValue = getGoalValue(conversionCountGoal, filters.start, filters.end)
      let conversionCountGoalRate = conversionCountGoalValue ? ((conversionCount / conversionCountGoalValue) * 100).toFixed(2) : 0

      let tickets = [
        {
          ...head[0],
          columns: head[0].columns.map((c, n) => {
            return n === 0
              ? {
                  ...c,
                  label: langMessages['modules.tickets'],
                }
              : c.key === 'goal-values'
                ? {
                    ...c,
                    colSpan: 4,
                  }
                : c
          }),
        },
        {
          ...head[1],
          columns: head[1].columns.reduce((all, c) => {
            if (c.key === 'result') {
              return [
                ...all,
                c,
                {
                  key: 'commission',
                  label: `${langMessages['payment.commissions']}`,
                  className: 'border dark-theme-border-darker text-center',
                },
              ]
            }
            return [...all, c]
          }, []),
        },
        {
          color: 'info',
          icon: 'fa fa-check',
          label: langMessages['texts.conversions'],
          sublabel: `${langMessages['stats.ticketsAverage']} R$${conversionTotalAvg}`,
          value: `R$ ${conversionTotal}`,
          goal: `R$ ${currencyFormat(conversionGoalValue)}`,
          result: <span className={parseFloat(conversionGoalRate) >= 100 ? 'text-success' : 'text-danger'}>{`${conversionGoalRate}%`}</span>,
          commission: 0,
          count: conversionCount,
          countGoal: conversionCountGoalValue.toFixed(0),
          countResult: (
            <span className={parseFloat(conversionCountGoalRate) >= 100 ? 'text-success' : 'text-danger'}>{`${conversionCountGoalRate}%`}</span>
          ),
        },
      ]

      let newState = { activity, deals, tickets, commissions: [] }
      setState(newState)
      return newState
    })
    .then(newState => {
      let where = []
      where.push(['updatedAt', '>=', filters.start])
      where.push(['updatedAt', '<=', filters.end])
      where.push([
        'status',
        'in',
        [
          CONSTANTS.PAGARME_PAID_STATUS,
          CONSTANTS.PAGARME_TRIALING_STATUS,
          CONSTANTS.PAGARME_PENDING_PAYMENT_STATUS,
          CONSTANTS.PAGARME_UNPAID_STATUS,
          CONSTANTS.PAGARME_ENDED_STATUS,
        ],
      ])

      if (collection === QIUSERS_COLLECTION_NAME) {
        return fetchPostsFromRefs(`${collection}/${postId}/${COLLECTIONS.COMMISSIONS_SUBCOLLECTION_NAME}`, where, 0, 'updatedAt', 'desc').then(
          commissions => {
            console.log('commissions', { postId, commissions })
            setState({ ...newState, commissions })
          }
        )
      } else if (collection === TEAMS_COLLECTION_NAME) {
        let promises = []
        ROLES.COMMISSIONED_ROLES.forEach(role => {
          let roleField = ROLES.QIPLUS_ROLES_FIELDS[role]
          if (Array.isArray(post[roleField]) && post[roleField].length) {
            post[roleField].forEach(qId => {
              promises.push(
                fetchPostsFromRefs(`${QIUSERS_COLLECTION_NAME}/${qId}/${COLLECTIONS.COMMISSIONS_SUBCOLLECTION_NAME}`, where, 0, 'updatedAt', 'desc')
              )
            })
          }
        })
        return Promise.all(promises).then(results => {
          const commissions = results.reduce((all, userCommissions) => [...all, ...userCommissions], [])
          console.log('commissions', { postId, commissions })
          setState({ ...newState, commissions })
        })
      }

      return null
    })
    .catch(err => console.error(err))
}
