import React from 'react'

import { langMessages } from '../../../lang'

import COLLECTIONS from '../../../constants/AppCollections'
import CONSTANTS from '../../../constants/AppConstants'

import AppModules from '../../../constants/AppModules'

// rct card box
import { RctCard } from 'Components/RctCard'

import { combineLogsStats, compileLogsStats, currencyFormat } from '../../../helpers/helpers'

import { Avatar, Fab, ListItem, ListItemText } from '@material-ui/core'
import { Link } from 'react-router-dom'

import { collections, dateFormat, compileTeamStats } from '../data/TeamsStats'
import { dataAccumulators, dataCounters } from '../helpers'

const TeamCard = ({ cols, goals, filters, logsFunc, post, team, logs, ...props }) => {
  let { collection, role, goalKey } = props

  let title = '',
    subtitle = AppModules[collection].singular,
    relatedField = '',
    detailLink = '',
    img = '',
    social = [],
    icon = AppModules[collection].icon

  let logsFields = { simple: '', composed: CONSTANTS.KEYWORDS_FIELD }

  const iniState = {
    activity: [],
    deals: [],
    tickets: [],
    commissions: [],
  }

  const [state, setState] = React.useState(iniState)

  if (post) {
    let postId = post.ID

    detailLink = `/${collection}/${postId}`

    let stats = {}
    collections.forEach(c => {
      let compiledStats = compileLogsStats(
        logs.filter(l => l.collection === c),
        c,
        dateFormat,
        dataCounters[c],
        dataAccumulators[c]
      )
      let response = { [postId]: compiledStats }
      let { collectionStats } = combineLogsStats({ response, collection: c, dateFormat })
      stats[c] = collectionStats
    })

    switch (collection) {
      case COLLECTIONS.QIUSERS_COLLECTION_NAME:
        relatedField = role
        title = post.displayName
        subtitle = langMessages[`roles.${role}`]
        img = post.avatar || ''
        // social = ['facebook','twitter','google','linkedin'].filter(s=>!!post[s])

        logsFields.simple = COLLECTIONS.FIRESTORE_INDEXES.simple.logs[role]

        break
      case COLLECTIONS.TEAMS_COLLECTION_NAME:
        relatedField = CONSTANTS.TEAM_FIELD
        title = post.title
        img = post.avatar || ''

        logsFields.simple = COLLECTIONS.FIRESTORE_INDEXES.simple.logs.team

        break
      default:
        break
    }

    React.useEffect(() => {
      console.log('useEffect > deps', [postId, filters.start, filters.end])
      compileTeamStats({ post, postId, team, collection, filters, goals, goalKey, relatedField, logsFunc, setState })
    }, [postId, filters.start, filters.end])
  }

  const { activity, deals, tickets, commissions } = state
  const commissionsVal = commissions.reduce((all, c) => all + c.data.total, 0)

  return (
    <div className={`user-profile-wrapper col-sm-${cols}`}>
      <RctCard customClasses="profile-head dark-theme-text-color">
        <div className="align-items-center bg-dark text-white border-bottom d-flex flex-column">
          <div className="user-image text-white text-center my-15">
            <Link to={detailLink}>
              {img ? (
                <Avatar alt={title || 'Imagem'} className="img-fluid" src={img} />
              ) : (
                <Avatar className={'bg-gray text-dark'}>
                  {icon ? <i className={icon}></i> : (title && title.charAt(0)) || (collection && collection.charAt(0))}
                </Avatar>
              )}
            </Link>
          </div>
          <div className="user-list-content">
            <div className="text-center">
              <h3 className="fw-bold">
                <Link to={detailLink}>{title}</Link>
              </h3>
              {subtitle && <p>{subtitle}</p>}
              {!!social.length && (
                <div className="social-list clearfix mb-40">
                  <ul className="list-inline d-inline-block mb-0">
                    {social.map(s => {
                      return (
                        <li className="list-inline-item" key={s}>
                          <Fab variant="circular" size="small" className={`btn-${s} text-white`}>
                            <i className={`zmdi zmdi-${s}`}></i>
                          </Fab>
                        </li>
                      )
                    })}
                  </ul>
                </div>
              )}
            </div>
          </div>
        </div>
        {!!activity.length && (
          <div className="user-activity text-center">
            <ul className="list-inline d-inline-block mb-0">
              {activity.map((a, k) => {
                return (
                  <li className="list-inline-item" key={k}>
                    <span className="fw-bold">{a.value}</span>
                    <span>{a.label}</span>
                  </li>
                )
              })}
            </ul>
          </div>
        )}
        {!!props.chart && props.chart}
        {[deals, tickets].map((b, i) => {
          return (
            !!b.length && (
              <div className="user-deals" key={i}>
                <table className="table m-0">
                  {b.filter(d => d.type === 'head').length && (
                    <thead>
                      {b
                        .filter(d => d.type === 'head')
                        .map((d, k) => {
                          let { columns } = d
                          return (
                            <tr key={k}>
                              {columns.map((c, n) => (
                                <th
                                  key={n}
                                  align={c.align || 'center'}
                                  className={c.className || ''}
                                  rowSpan={c.rowSpan || 1}
                                  colSpan={c.colSpan || 1}
                                >
                                  {c.label}
                                </th>
                              ))}
                            </tr>
                          )
                        })}
                    </thead>
                  )}
                  <tbody>
                    {b
                      .filter(d => d.type !== 'head')
                      .map((d, k) => {
                        return (
                          <tr key={k}>
                            <td>
                              <div className="list-divider">
                                <ListItem component="div" className="py-1 px-15">
                                  {!!d.icon && (
                                    <Avatar className={`mr-15 bg-${d.color || ''}`}>
                                      <i className={`zmdi-hc-lg text-white ${d.icon}`}></i>
                                    </Avatar>
                                  )}
                                  <ListItemText className="flex-1" primary={d.label} secondary={d.sublabel || ''} />
                                  <div className="flex-0"></div>
                                </ListItem>
                              </div>
                            </td>
                            <td align="center" className="border dark-theme-border-darker">
                              <span className="fw-bold">{d.value}</span>
                            </td>
                            <td align="center" className="border dark-theme-border-darker">
                              <span className="fw-bold">{d.goal || ''}</span>
                            </td>
                            <td align="center" className="border dark-theme-border-darker">
                              <span className="fw-bold">{d.result || ''}</span>
                            </td>
                            {'commission' in d && (
                              <td align="center" className="border dark-theme-border-darker">
                                <span className="fw-bold">R${currencyFormat(commissionsVal || 0)}</span>
                              </td>
                            )}
                            <td align="center" className="border dark-theme-border-darker">
                              <span className="fw-bold">{d.count}</span>
                            </td>
                            <td align="center" className="border dark-theme-border-darker">
                              <span className="fw-bold">{d.countGoal || ''}</span>
                            </td>
                            <td align="center" className="border dark-theme-border-darker">
                              <span className="fw-bold">{d.countResult || ''}</span>
                            </td>
                          </tr>
                        )
                      })}
                  </tbody>
                </table>
              </div>
            )
          )
        })}
      </RctCard>
    </div>
  )
}

export default TeamCard
