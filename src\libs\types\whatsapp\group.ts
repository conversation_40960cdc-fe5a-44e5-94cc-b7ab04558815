export interface WhatsAppGroupMember {
  id: string
  firstName: string
  lastName?: string
  phone: string
  displayName?: string
}

export interface WhatsAppGroup {
  id: string
  name: string
  description?: string
  members: WhatsAppGroupMember[]
  createdAt: string
  instanceId: string
  accountId: string
  groupJid?: string
  status: 'pending' | 'created' | 'failed'
  error?: string
}

export interface GroupCreationRequest {
  instanceId: string
  accountId: string
  userId: string
  groupPrefix: string
  leads: WhatsAppGroupMember[]
  maxLeadsPerGroup?: number
}

export interface GroupCreationResponse {
  success: boolean
  totalGroups: number
  createdGroups: WhatsAppGroup[]
  failedGroups: Array<{
    name: string
    error: string
    members: WhatsAppGroupMember[]
  }>
  message?: string
}

export interface GroupCreationProgress {
  total: number
  completed: number
  current: string
  status: 'creating' | 'completed' | 'failed'
  groups: WhatsAppGroup[]
}

export interface CreateGroupRequest {
  instanceId: string
  accountId: string
  groupName: string
  description?: string
  members: string[] // phone numbers
}

export interface CreateGroupResponse {
  success: boolean
  groupId?: string
  groupJid?: string
  error?: string
  message?: string
  details?: any
}
