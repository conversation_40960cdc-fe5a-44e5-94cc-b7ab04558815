/**
 * Flex Grid
 */
import React from 'react'
import { ImageList, ImageListItem, ImageListItemBar, IconButton } from '@material-ui/core'

// lang strings
import { langMessages } from '../../../lang'

// rct section loader
import RctSectionLoader from 'Components/RctSectionLoader/RctSectionLoader'
import { Alert } from 'reactstrap'
import Skeleton from '@material-ui/lab/Skeleton'

function FlexGridList(props) {
  const onClick = props.onClick || (() => {})
  const gridProps = props.gridProps || {}
  const rowHeight = props.rowHeight || gridProps.rowHeight || 200
  const gap = props.gap || gridProps.gap || 200
  const cols = props.cols || gridProps.cols || 3

  const items = props.fetching
    ? [
        {
          selected: true,
          children: (
            <div className="bg-white bd-1 border-light-blue ">
              <Skeleton variant="rect" height={20} width="100%" />
              <div className="position-relative " style={{ paddingTop: rowHeight - 68 - 20 }}>
                <RctSectionLoader />
              </div>
              <div style={{ height: rowHeight - 68 - 20, padding: 20, backgroundColor: 'rgba(70, 77, 105, 0.75)' }}>
                <Skeleton variant="text" height={18} width="80%" />
                <Skeleton variant="text" height={14} width="40%" />
              </div>
            </div>
          ),
        },
      ]
    : props.items

  return (
    <div
      className={`FlexGridListComponent FlexGridList FlexGridList-wrapper ${props.wrapperClasses || ''} ${props.selectable ? 'FlexGridList-selectable' : ''}`}
    >
      {!!props.errorFetching && (
        <div className="p-30">
          <Alert className="mb-15" fade={false} color="warning" isOpen={true}>
            {langMessages['errors.errorFetchingPosts']}
          </Alert>
        </div>
      )}
      <ImageList cols={cols} rowHeight={rowHeight} gap={gap} {...gridProps}>
        {items.map((item, k) => {
          const icon = item.icon ? item.icon : props.icon || (props.selectable ? (item.selected ? 'fa fa-check-circle' : 'fa fa-circle-thin') : '')
          return (
            <ImageListItem
              key={k}
              onClick={e => onClick(item)}
              className={`FlexGridList-item ${item.selected ? 'selected' : ''} ${item.classes || ''}`}
            >
              <div className="h-100 w-100">
                {(!!item.img || !!item.thumbnail) && <img src={item.img || item.thumbnail} alt={item.title} />}
                {!!item.children && item.children}
              </div>
              {(!!item.title || !!item.subtitle) && (
                <ImageListItemBar
                  title={item.title}
                  subtitle={<span>{item.subtitle || ''}</span>}
                  actionIcon={
                    <IconButton>
                      {' '}
                      <i className={`text-white ${icon}`}></i>{' '}
                    </IconButton>
                  }
                />
              )}
            </ImageListItem>
          )
        })}
      </ImageList>
    </div>
  )
}

export default FlexGridList
