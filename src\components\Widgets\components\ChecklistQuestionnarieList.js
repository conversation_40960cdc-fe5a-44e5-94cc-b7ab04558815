import React from 'react'
import Button from '@material-ui/core/Button'
import Dialog from '@material-ui/core/Dialog'
import DialogActions from '@material-ui/core/DialogActions'
import DialogContent from '@material-ui/core/DialogContent'
import DialogContentText from '@material-ui/core/DialogContentText'
import DialogTitle from '@material-ui/core/DialogTitle'
import ChecklistQuestionnarieItem from './ChecklistQuestionnarieItem'

export default function ChecklistQuestionnarieList({ questions }) {
  const [open, setOpen] = React.useState(false)
  const [currentQuestion, setCurrentQuestion] = React.useState({})

  const handleClickOpen = () => {
    setOpen(true)
  }

  const handleClose = () => {
    setOpen(false)
  }

  return (
    <div>
      <h5> Questionario </h5>
      {questions.map((question, i) => {
        return (
          <div key={i}>
            <Button
              color="primary"
              size="small"
              onClick={() => {
                setCurrentQuestion(question)
                handleClickOpen()
              }}
            >
              {question.question}
            </Button>
          </div>
        )
      })}
      {!!currentQuestion && (
        <Dialog open={open} onClose={handleClose} aria-labelledby="form-dialog-title" fullWidth>
          <DialogTitle id="form-dialog-title" onClose={handleClose}>
            {currentQuestion.question}
          </DialogTitle>
          <DialogContent>
            <DialogContentText>Respostas</DialogContentText>
            {currentQuestion.choices !== null ? (
              <ChecklistQuestionnarieItem choices={currentQuestion.choices} type={currentQuestion.type} disabled />
            ) : (
              <DialogContentText> Não existem opções para a pergunta.</DialogContentText>
            )}
          </DialogContent>
          <DialogActions>
            <Button autoFocus onClick={handleClose} color="primary">
              Fechar
            </Button>
          </DialogActions>
        </Dialog>
      )}
    </div>
  )
}
