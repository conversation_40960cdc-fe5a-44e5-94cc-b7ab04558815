/**
 * Modules Area Chart Widget
 */
import React from 'react'
import CountUp from 'react-countup'

import { LinearProgress } from '@material-ui/core'

// chart
import Tiny<PERSON><PERSON><PERSON><PERSON> from 'Components/Charts/TinyAreaChart'

// lang strings
import { langMessages } from '../../../lang'

// rct card box
import { RctCard, RctCardContent } from 'Components/RctCard'

// Alert
import Alert from 'reactstrap/lib/Alert'

// helpers
import { hexToRgbA } from 'Helpers/helpers'

// Constants
import AppModules from '../../../constants/AppModules'

const AppModuleWidget = props => {
  const { stats, chartData, fetching, error, collection, title } = props

  const widgetName = title

  return (
    <RctCard>
      <RctCardContent>
        <div className="clearfix">
          <div className="float-left">
            <h3 className="mb-15 fw-semi-bold">{widgetName}</h3>
            <div className="d-flex">
              <div className="text-center mr-50">
                <span className="fs-14 d-block">{langMessages['widgets.daily']}</span>
                <CountUp separator="," className="counter-point" start={0} end={stats.daily || 0} duration={5} useEasing={true} />
              </div>
              <div className="text-center mr-50">
                <span className="fs-14 d-block">{langMessages['widgets.weekly']}</span>
                <CountUp separator="," className="counter-point" start={0} end={stats.weekly || 0} duration={5} useEasing={true} />
              </div>
              <div className="text-center mr-50">
                <span className="fs-14 d-block">{langMessages['widgets.monthly']}</span>
                <CountUp separator="," className="counter-point" start={0} end={stats.monthly || 0} duration={5} useEasing={true} />
              </div>
              <div className="text-center">
                <span className="fs-14 d-block">{langMessages['widgets.total']}</span>
                <CountUp separator="," className="counter-point" start={0} end={stats.total || 0} duration={5} useEasing={true} />
              </div>
            </div>
          </div>
          <div className="float-right hidden-md-down">
            <div className="featured-section-icon">
              <i className={AppModules[collection].icon}></i>
            </div>
          </div>
        </div>
      </RctCardContent>
      {error ? (
        <Alert color="danger">{langMessages['errors.fetchErrorMsg']}</Alert>
      ) : (
        <TinyAreaChart
          label={widgetName}
          datasets={chartData.datasets || [{ data: chartData.data }]}
          labels={chartData.labels}
          options={{
            scales: {
              xAxes: [
                {
                  display: true,
                },
              ],
              yAxes: [
                {
                  display: false,
                },
              ],
            },
          }}
          backgroundColor={hexToRgbA(AppModules[collection].color, 0.1)}
          borderColor={hexToRgbA(AppModules[collection].color, 3)}
          lineTension="0"
          height={70}
          gradient
          hideDots
        />
      )}
      {fetching && <LinearProgress />}
    </RctCard>
  )
}

export default AppModuleWidget
