/**
 * Inset List
 */
import React from 'react'

import MenuIcon from '@material-ui/icons/Menu'

import { UncontrolledDropdown, DropdownToggle, DropdownMenu } from 'reactstrap'
import { IconButton, Button } from '@material-ui/core'

const SimpleDropdown = ({ icon, children, ...props }) => (
  <UncontrolledDropdown className="simple-dropdown">
    <DropdownToggle tag="span" className="bd-0 bg-none shadow-none pb-0 px-0">
      {icon ? (
        <Button size="small" /* variant="contained" color="primary" */ className="">
          <i className="material-icons">add</i>
        </Button>
      ) : (
        <IconButton className="p-5">
          <MenuIcon className="text-dark" />
        </IconButton>
      )}
    </DropdownToggle>
    <DropdownMenu className="DropdownMenu-small font-sm">{children}</DropdownMenu>
  </UncontrolledDropdown>
)

export default SimpleDropdown
