import { Typography } from "@material-ui/core";
import React from "react";
import CountUp from "react-countup";


function CountUpWidget(props) {

  const { start, end, title } = props;
  let startNumber = parseInt(start, 10)
  let finishNumber = parseInt(end, 10)

  return (
    <div className="countup-widget">
      <Typography variant="h6">{title}</Typography>

      <Typography variant="h3">
        <CountUp start={startNumber} end={finishNumber} duration={5} />
      </Typography>
    </div>
  )
}

export default CountUpWidget;
