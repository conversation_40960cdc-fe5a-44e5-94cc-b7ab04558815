/**
 * Debug script for WhatsApp Group Creation Issues
 * 
 * This script helps diagnose the 400 Bad Request error you're experiencing
 * Run with: node debug-group-creation.js
 */

const axios = require('axios')

// Configuration - Update these with your actual values
const BASE_URL = 'http://localhost:8000' // Your server URL
const INSTANCE_ID = 'test-instance-123'
const ACCOUNT_ID = 'test-account-123'
const USER_ID = 'test-user-123'
const AUTH_TOKEN = 'your-auth-token-here' // Replace with actual token

// Test different phone number formats
const phoneNumberTests = [
  {
    name: 'Brazilian Mobile (with country code)',
    phones: ['*************']
  },
  {
    name: 'Brazilian Mobile (formatted)',
    phones: ['+55 11 99999-9999']
  },
  {
    name: 'Brazilian Mobile (with spaces)',
    phones: ['55 11 99999 9999']
  },
  {
    name: 'Brazilian Mobile (without country code)',
    phones: ['***********']
  },
  {
    name: 'Multiple valid phones',
    phones: ['*************', '*************']
  },
  {
    name: 'Invalid phone (too short)',
    phones: ['123456']
  },
  {
    name: 'Invalid phone (letters)',
    phones: ['abc123def']
  },
  {
    name: 'Mixed valid and invalid',
    phones: ['*************', 'invalid', '*************']
  }
]

async function testPhoneValidation() {
  console.log('🧪 Testing Phone Number Validation')
  console.log('=' .repeat(50))
  
  for (const test of phoneNumberTests) {
    console.log(`\n📱 Testing: ${test.name}`)
    console.log(`Input: ${JSON.stringify(test.phones)}`)
    
    try {
      const response = await axios.post(
        `${BASE_URL}/whatsapp/groups/create/${INSTANCE_ID}`,
        {
          accountId: ACCOUNT_ID,
          userId: USER_ID,
          groupName: `Test Group - ${test.name}`,
          description: 'Test group for debugging',
          members: test.phones
        },
        {
          headers: {
            'Authorization': `Bearer ${AUTH_TOKEN}`,
            'Content-Type': 'application/json'
          }
        }
      )
      
      console.log('✅ Success:', response.data)
      
    } catch (error) {
      console.log('❌ Error:')
      console.log('- Status:', error.response?.status)
      console.log('- Error:', error.response?.statusText)
      console.log('- Details:', JSON.stringify(error.response?.data, null, 2))
    }
  }
}

async function testGroupNameValidation() {
  console.log('\n🧪 Testing Group Name Validation')
  console.log('=' .repeat(50))
  
  const groupNameTests = [
    { name: 'Normal name', groupName: 'Test Group' },
    { name: 'Empty name', groupName: '' },
    { name: 'Whitespace only', groupName: '   ' },
    { name: 'Very long name', groupName: 'A'.repeat(100) },
    { name: 'Special characters', groupName: 'Test Group @#$%' },
    { name: 'Unicode characters', groupName: 'Grupo de Teste 🚀' }
  ]
  
  for (const test of groupNameTests) {
    console.log(`\n📝 Testing: ${test.name}`)
    console.log(`Group name: "${test.groupName}"`)
    
    try {
      const response = await axios.post(
        `${BASE_URL}/whatsapp/groups/create/${INSTANCE_ID}`,
        {
          accountId: ACCOUNT_ID,
          userId: USER_ID,
          groupName: test.groupName,
          description: 'Test group for debugging',
          members: ['*************']
        },
        {
          headers: {
            'Authorization': `Bearer ${AUTH_TOKEN}`,
            'Content-Type': 'application/json'
          }
        }
      )
      
      console.log('✅ Success:', response.data)
      
    } catch (error) {
      console.log('❌ Error:')
      console.log('- Status:', error.response?.status)
      console.log('- Error:', error.response?.statusText)
      console.log('- Details:', JSON.stringify(error.response?.data, null, 2))
    }
  }
}

async function testEvolutionAPIDirectly() {
  console.log('\n🧪 Testing Evolution API Directly (if possible)')
  console.log('=' .repeat(50))
  
  // This would test the Evolution API directly if you have the credentials
  // You'll need to replace these with your actual Evolution API details
  const EVOLUTION_API_URL = process.env.WHATSAPP_API_URL
  const EVOLUTION_API_TOKEN = process.env.WHATSAPP_API_TOKEN
  
  if (!EVOLUTION_API_URL || !EVOLUTION_API_TOKEN) {
    console.log('⚠️ Evolution API credentials not found in environment variables')
    console.log('Set WHATSAPP_API_URL and WHATSAPP_API_TOKEN to test directly')
    return
  }
  
  try {
    const response = await axios.post(
      `${EVOLUTION_API_URL}/group/create/${INSTANCE_ID}`,
      {
        subject: 'Direct Test Group',
        description: 'Testing Evolution API directly',
        participants: ['*************']
      },
      {
        headers: {
          'Content-Type': 'application/json',
          'apikey': EVOLUTION_API_TOKEN
        }
      }
    )
    
    console.log('✅ Direct Evolution API Success:', response.data)
    
  } catch (error) {
    console.log('❌ Direct Evolution API Error:')
    console.log('- Status:', error.response?.status)
    console.log('- Error:', error.response?.statusText)
    console.log('- Details:', JSON.stringify(error.response?.data, null, 2))
    console.log('- Request URL:', `${EVOLUTION_API_URL}/group/create/${INSTANCE_ID}`)
    console.log('- Request Headers:', {
      'Content-Type': 'application/json',
      'apikey': EVOLUTION_API_TOKEN ? '[HIDDEN]' : 'NOT_SET'
    })
  }
}

async function testMinimalRequest() {
  console.log('\n🧪 Testing Minimal Valid Request')
  console.log('=' .repeat(50))
  
  const minimalRequest = {
    accountId: ACCOUNT_ID,
    userId: USER_ID,
    groupName: 'Minimal Test',
    members: ['*************']
  }
  
  console.log('Request body:', JSON.stringify(minimalRequest, null, 2))
  
  try {
    const response = await axios.post(
      `${BASE_URL}/whatsapp/groups/create/${INSTANCE_ID}`,
      minimalRequest,
      {
        headers: {
          'Authorization': `Bearer ${AUTH_TOKEN}`,
          'Content-Type': 'application/json'
        }
      }
    )
    
    console.log('✅ Minimal request success:', response.data)
    
  } catch (error) {
    console.log('❌ Minimal request error:')
    console.log('- Status:', error.response?.status)
    console.log('- Error:', error.response?.statusText)
    console.log('- Details:', JSON.stringify(error.response?.data, null, 2))
  }
}

async function runDiagnostics() {
  console.log('🔍 WhatsApp Group Creation Diagnostics')
  console.log('🗝️ Debugging 400 Bad Request Error')
  console.log('=' .repeat(60))
  
  console.log('\n📋 Configuration:')
  console.log(`- Base URL: ${BASE_URL}`)
  console.log(`- Instance ID: ${INSTANCE_ID}`)
  console.log(`- Account ID: ${ACCOUNT_ID}`)
  console.log(`- Auth Token: ${AUTH_TOKEN ? '[SET]' : '[NOT SET]'}`)
  
  // Test minimal request first
  await testMinimalRequest()
  
  // Test phone number validation
  await testPhoneValidation()
  
  // Test group name validation
  await testGroupNameValidation()
  
  // Test Evolution API directly (if credentials available)
  await testEvolutionAPIDirectly()
  
  console.log('\n✨ Diagnostics completed!')
  console.log('\n💡 Common causes of 400 Bad Request:')
  console.log('1. Invalid phone number format')
  console.log('2. Missing required fields')
  console.log('3. Invalid instance ID or account ID')
  console.log('4. Evolution API authentication issues')
  console.log('5. Group name validation issues')
  console.log('\n🔧 Check the server logs for detailed error messages')
}

// Run diagnostics if this file is executed directly
if (require.main === module) {
  runDiagnostics().catch(console.error)
}

module.exports = {
  testPhoneValidation,
  testGroupNameValidation,
  testEvolutionAPIDirectly,
  testMinimalRequest,
  runDiagnostics
}
