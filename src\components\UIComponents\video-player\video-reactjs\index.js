//Video Player

import React, { Component } from 'react'

import {
  Player,
  ControlBar,
  ReplayControl,
  ForwardControl,
  CurrentTimeDisplay,
  TimeDivider,
  PlaybackRateMenuButton,
  VolumeMenuButton,
  BigPlayButton,
} from 'video-react'
//Components

import DownloadButton from './component/DownloadButton'

// intl messages
import IntlMessages from 'Util/IntlMessages'

// rct card box
import RctCollapsibleCard from 'Components/RctCollapsibleCard/RctCollapsibleCard'

const Shortcut = [
  {
    id: 1,
    action1: 'Increase speed',
    shortcut1: 'Shift + >',
    action2: 'Decrease speed',
    shortcut2: 'Shift + <',
  },
  {
    id: 2,
    action1: 'Go back 5 seconds',
    shortcut1: 'Left arrow',
    action2: 'Go forward 5 seconds',
    shortcut2: 'Right arrow',
  },
  {
    id: 3,
    action1: 'Go back 10 seconds',
    shortcut1: 'j',
    action2: 'Go forward 10 seconds',
    shortcut2: 'l',
  },
  {
    id: 4,
    action1: 'Go to Full Screen mode',
    shortcut1: 'f',
    action2: 'Exit Full Screen mode',
    shortcut2: 'Escape',
  },
  {
    id: 5,
    action1: 'Increase volume 5%',
    shortcut1: 'Up arrow',
    action2: 'Decrease volume 5%',
    shortcut2: 'Down arrow',
  },
  {
    id: 6,
    action1: 'Toggle play/pause the video',
    shortcut1: 'k or Spacebar',
    action2: '',
    shortcut2: '',
  },
]

class VideoPlayer extends Component {
  state = {
    displayShortcuts: false,
  }

  renderPlayer() {
    return (
      <Player playsInline={!!this.props.playsInline} poster={this.props.poster || ''} src={this.props.src || ''}>
        <ControlBar autoHide={'autoHide' in this.props ? this.props.autoHide : true}>
          <BigPlayButton position="center" />
          <ReplayControl seconds={10} order={1.1} />
          <ForwardControl seconds={30} order={1.2} />
          <CurrentTimeDisplay order={4.1} />
          <TimeDivider order={4.2} />
          <PlaybackRateMenuButton rates={[5, 2, 1, 0.5, 0.1]} order={7.1} />
          <DownloadButton order={7} />
          <VolumeMenuButton />
        </ControlBar>
      </Player>
    )
  }

  render() {
    return (
      <div className="video-player-wrapper">
        {this.props.unwrap === false ? (
          <RctCollapsibleCard colClasses={this.props.colClasses || ''} heading={this.props.heading || ''}>
            {this.renderPlayer()}
          </RctCollapsibleCard>
        ) : (
          this.renderPlayer()
        )}
        {this.state.displayShortcuts && (
          <RctCollapsibleCard colClasses="col-sm-12 col-md-12 col-lg-12" heading={<IntlMessages id="widgets.keyboardShortcuts" />}>
            <div className="table-responsive">
              <table className="table table-hover mb-0">
                <thead>
                  <tr>
                    <th>Action</th>
                    <th>Shortcut</th>
                    <th>Action</th>
                    <th>Shortcut</th>
                  </tr>
                </thead>
                <tbody>
                  {Shortcut &&
                    Shortcut.map((data, key) => (
                      <tr key={key}>
                        <td>{data.action1}</td>
                        <td>{data.shortcut1}</td>
                        <td>{data.action2}</td>
                        <td>{data.shortcut2}</td>
                      </tr>
                    ))}
                </tbody>
              </table>
            </div>
          </RctCollapsibleCard>
        )}
      </div>
    )
  }
}
export default VideoPlayer
