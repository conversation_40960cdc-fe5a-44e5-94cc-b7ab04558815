/**
 * Todo List Item Component
 */
import React from 'react'
import FormControlLabel from '@material-ui/core/FormControlLabel'
import Checkbox from '@material-ui/core/Checkbox'
import classNames from 'classnames'

// lang strings
import { langMessages } from '../../../lang'

// helpers
import { localJSON, sTrim, onlyNumbers } from 'Helpers'

// Model
import TasklistModel from 'Routes/tasklists/model'
import notificationModel from 'Models/childnodes/notification'
import deadlineModel from 'Models/childnodes/deadline'

import { Button, TextField, Dialog, DialogTitle, DialogActions, DialogContent, IconButton } from '@material-ui/core'

import { DropdownItem } from 'reactstrap'
import SimpleDropdown from 'Components/UIComponents/dropdown/SimpleDropdown'
import Deadline from 'Routes/components/Deadline'
import Notifications from 'Routes/components/Notifications'

const ChecklistItem = ({ checklist, task, index, editable, preview, onToggleTaskItem, onRemove, onUpdate }) => {
  const user = localJSON.get('user', false)
  if (task.deleted) return null

  const [isChecked, toggleCheck] = React.useState(!!task.completed)
  const [isEditing, toggleEdit] = React.useState(task.focus)
  const [dialogOpen, toggleDialog] = React.useState('')

  const onUpdateTask = obj => {
    // console.log('onUpdateTask', obj, 'task', task)
    onUpdate({ ...task, ...obj })
  }

  const checkImportance = task => {
    if (checklist?.score === true) {
      const newImportance = task.scoreValue ? task.scoreValue : (task.scoreValue = 1)
      return newImportance
    }
    return ''
  }
  // tasks
  let tasksModel = window.jsonClone(TasklistModel.tasks)
  let singleTaskModel = window.jsonClone(tasksModel[0])

  task = { ...singleTaskModel, ...task }
  task.scoreValue = checkImportance(task)

  let deadline = {
    ...window.jsonClone(deadlineModel),
    ...task.deadline,
  }

  const isFutureEvent = !deadline.time || new Date(deadline.time).getTime() > new Date().getTime() || deadline.type === 'oncreate'
  let notification = deadline.notification && isFutureEvent

  // notifications
  const newNotification = {
    ...window.jsonClone(notificationModel),
    qiuser: user.ID,
    context: {
      collection: checklist.collection,
      id: checklist.ID,
      operator_id: user.ID,
    },
  }
  let notifications = (task.notifications || [window.jsonClone(newNotification)]).map(n => ({ ...n, ...newNotification }))

  return (
    <div className={classNames('tasklist-item checkist-item list-item', { strike: task.completed })} key={index}>
      <div
        className={classNames('checkist-item-wrapper list-item-wrapper d-flex justify-content-between align-items-center', {
          preview: preview === true,
          editable: editable === true,
        })}
      >
        {
          // preview!== true &&
          <div className="checkist-item-checker list-item-checker">
            {(editable || isEditing) && <div className="mt-5"></div>}
            <FormControlLabel
              control={
                <Checkbox
                  color="primary"
                  disabled={preview === true}
                  checked={isChecked}
                  onClick={e => {
                    e.stopPropagation()
                    toggleCheck(!isChecked)
                    onToggleTaskItem(index)
                  }}
                />
              }
            />
          </div>
        }
        <div
          className="flex-1 checkist-item-body list-item-body"
          onClick={e => {
            if (preview === true || editable === true) return
            e.stopPropagation()
            toggleCheck(!isChecked)
            onToggleTaskItem(index)
          }}
        >
          {(!editable || !isEditing) && (
            <div className="row">
              <span className="mb-0 mt-5 col-sm-9 col-md-9" onClick={e => toggleEdit(!isEditing)}>
                {task.title}
              </span>
              {checklist.score ? (
                <span className="mb-0 mt-5 col-sm-3 col-md-3 align-items-center" onClick={e => toggleEdit(!isEditing)}>
                  {task.scoreValue}
                </span>
              ) : (
                ''
              )}
            </div>
          )}
          {editable && isEditing && (
            <div className="row">
              <div className="col-sm-9 col-md-9">
                {
                  <TextField
                    placeholder={langMessages['tasklists.label.title']}
                    value={task.title}
                    inputProps={{ spellCheck: false }}
                    onBlur={e => {
                      if (!sTrim(task.title)) {
                        onRemove(index)
                      } else {
                        onUpdateTask({ focus: false })
                      }
                      toggleEdit(false)
                    }}
                    onChange={e => onUpdateTask({ title: e.target.value })}
                    onKeyPress={e => {
                      if (e.which === 13) {
                        onUpdateTask({ focus: false })
                        toggleEdit(false)
                      }
                    }}
                  />
                }
              </div>
              <div className="col-sm-3 col-md-3 align-items-center">
                {checklist.score && (
                  <TextField
                    placeholder={langMessages['tasklists.label.importance']}
                    value={task.scoreValue}
                    inputProps={{ spellCheck: false }}
                    onBlur={e => {
                      if (!sTrim(task.scoreValue)) {
                        onRemove(index)
                      } else {
                        onUpdateTask({ focus: false })
                      }
                      toggleEdit(false)
                    }}
                    onChange={e => onUpdateTask({ scoreValue: onlyNumbers(e.target.value) })}
                    onKeyPress={e => {
                      if (e.which === 13) {
                        onUpdateTask({ focus: false })
                        toggleEdit(false)
                      }
                    }}
                  />
                )}
              </div>
            </div>
          )}
        </div>
        <div>
          {editable && (
            <SimpleDropdown>
              {/* <DropdownItem onClick={e=>toggleDialog(dialogOpen==='notifications'?'':'notifications')} className="px-10">{langMessages["widgets.notifications"]}</DropdownItem> */}
              <DropdownItem onClick={e => toggleDialog(dialogOpen === 'deadline' ? '' : 'deadline')} className="px-10">
                {langMessages['time.deadline']}
              </DropdownItem>
              {/* <hr className="my-5"/> */}
              <DropdownItem onClick={e => onRemove(index)} className="px-10">
                {langMessages['button.delete']}
              </DropdownItem>
            </SimpleDropdown>
          )}
        </div>
        {dialogOpen === 'notifications' && (
          <Dialog
            open={true}
            maxWidth="sm"
            onClose={() => toggleDialog('')}
            aria-labelledby="alert-dialog-title"
            aria-describedby="alert-dialog-description"
            classes={{ paper: 'w-80' }}
          >
            <DialogTitle id="alert-dialog-title">{langMessages['widgets.notifications']}</DialogTitle>
            <DialogContent>
              <div className="py-30">
                <Notifications post={checklist} notifications={notifications} onUpdate={onUpdateTask} />
              </div>
            </DialogContent>
            <DialogActions>
              <Button
                className="btn-primary text-white"
                autoFocus
                onClick={() => {
                  toggleDialog('')
                }}
              >
                {langMessages['button.ok']}
              </Button>
            </DialogActions>
          </Dialog>
        )}
      </div>
      {dialogOpen === 'deadline' && (
        <div className="py-10">
          {/* <hr className="mt-0 mb-15"> */}
          <div className="d-flex justify-content-between">
            <div className="flex-1">
              <Deadline deadline={deadline} onUpdate={onUpdateTask} />
            </div>
            <IconButton
              autoFocus
              size="small"
              color="primary"
              onClick={() => {
                toggleDialog('')
              }}
            >
              <i className="text-dark icon-check" />
            </IconButton>
          </div>
          <hr className="mt-15 mb-0" />
        </div>
      )}
    </div>
  )
}

export default ChecklistItem
