import React from 'react'
import <PERSON><PERSON><PERSON><PERSON><PERSON> from '../../Charts/TinyAreaChart'

import ChartConfig from '../../../constants/chart-config'
import ThemeColors from '../../../constants/ThemeColors'

import { hexToRgbA } from '../../../helpers/helpers'
import { List, ListItem } from '@material-ui/core'
import { Badge } from 'reactstrap'

const StatsLineChart = ({
  stats,
  chartData,
  chartOptions,
  height,
  dateFormat,
  labelFormat,
  areaChartTriggers,
  areaChartColors,
  areaChartLabels,
  filters,
  ...props
}) => {
  height = height || 120

  const iniState = {
    order: null,
    orderBy: null,
  }

  const [state, setState] = React.useState(iniState)

  let { order, orderBy } = state

  let { datasets, labels } = chartData

  chartOptions = {
    elements: {
      point: { radius: 3 },
      ...((chartOptions || {}).elements || {}),
    },
    legend: {
      display: true,
      fullWidth: true,
      position: 'bottom',
      align: 'start',
      labels: { padding: 20 },
      ...((chartOptions || {}).legend || {}),
    },
    scales: {
      xAxes: [{ display: true }],
      yAxes: [{ display: true }],
      ...((chartOptions || {}).scales || {}),
    },
    ...(chartOptions || {}),
  }

  let valueReducer = a => a.reduce((all, v) => all + v, 0)
  let ranking = datasets
    .filter(d => d.label)
    .sort((a, b) => valueReducer(b.data) - valueReducer(a.data))
    .filter((r, i) => i < 3)

  let groups = datasets
    .filter(s => s.group)
    .reduce((all, set) => [...all.filter(s => s.group !== set.group), set], [])
    .map(s => s.group)

  let postIds = datasets
    .filter(s => s.postId)
    .map(set => ({
      ...set,
      data: !orderBy || set.group === orderBy ? set.data : [],
    }))
    .reduce(
      (all, set) => [
        ...all.filter(s => s.postId !== set.postId),
        {
          ...set,
          data: [...set.data, ...((all.find(s => s.postId === set.postId) || {}).data || [])],
        },
      ],
      []
    )
    .sort((a, b) => {
      let A = order === 'asc' ? b : a
      let B = order === 'asc' ? a : b
      return valueReducer(B.data) - valueReducer(A.data)
    })
    .filter((r, i) => i < 3)
    .map(s => s.postId)

  // console.log({ groups, postIds });

  return (
    <div className="support-widget-wrap d-flex flex-column flex-1">
      {!!props.title && <h4>{props.title}</h4>}
      <div className="text-center pt-20 flex-1">
        <TinyAreaChart
          label="Visitors"
          labels={labels}
          datasets={datasets}
          options={chartOptions}
          backgroundColor={hexToRgbA(ChartConfig.color.primary, 0.1)}
          borderColor={hexToRgbA(ChartConfig.color.primary, 3)}
          lineTension="0"
          height={height}
          hideDots={false}
          gradient
        />
      </div>
      {groups.length > 1 && postIds.length ? (
        <table>
          <thead>
            <tr>
              <th></th>
              <th>
                <ListItem className="bg-light px-15 py-5">
                  <div className="content-title">&nbsp;</div>
                </ListItem>
              </th>
              {groups.map(group => (
                <th
                  className="text-center pointer"
                  onClick={e =>
                    setState({
                      ...state,
                      order: order === 'asc' ? null : order === 'desc' ? 'asc' : 'desc',
                      orderBy: order === 'asc' ? null : group,
                    })
                  }
                  key={group}
                >
                  <b className="fs-12">{areaChartLabels[group] || group}</b>
                  {orderBy === group && <i className={`ml-1 fa fa-sort-${order} align-${order === 'desc' ? 'top' : 'bottom'}`} />}
                </th>
              ))}
            </tr>
          </thead>
          <tbody>
            {postIds.map(postId => {
              let set = datasets.find(s => s.postId === postId)
              let charLimit = 40
              let { postTitle } = set
              let colorKey = Object.keys(ThemeColors).find(k => ThemeColors[k] === set.borderColor)
              return (
                <tr key={postId}>
                  <td align="center" className="bg-light">
                    <Badge pill style={{ lineHeight: 0 }} className={`py-1 bg-${colorKey}`}>
                      &nbsp;
                    </Badge>
                  </td>
                  <td>
                    <ListItem className="bg-light px-15 py-5" key={postId}>
                      <div className="content-title">{postTitle.substr(0, charLimit) + (postTitle.length > charLimit ? '...' : '')}</div>
                    </ListItem>
                  </td>
                  {groups
                    .map(group => datasets.find(s => s.postId === postId && s.group === group))
                    .map((d, k) => (
                      <td align="center" className="bg-light" key={k}>
                        <strong className={''}>{d ? valueReducer(d.data) : 0}</strong>
                      </td>
                    ))}
                </tr>
              )
            })}
          </tbody>
        </table>
      ) : (
        !!ranking.length &&
        props.ranking !== false && (
          <List className="list-unstyled p-0 mt-10 flex-1 bg-light">
            {ranking.map((d, k) => {
              let charLimit = 40
              let fullLabel = d.label
              let colorKey = Object.keys(ThemeColors).find(k => ThemeColors[k] === d.borderColor)
              return (
                <ListItem className="bg-light px-15 py-5 d-flex justify-content-between align-content-center" key={k}>
                  <p className="mb-0 content-title">{fullLabel.substr(0, charLimit) + (fullLabel.length > charLimit ? '...' : '')}</p>
                  <Badge className={`px-4 bg-${colorKey}`}>{valueReducer(d.data)}</Badge>
                </ListItem>
              )
            })}
          </List>
        )
      )}
    </div>
  )
}

export default StatsLineChart
