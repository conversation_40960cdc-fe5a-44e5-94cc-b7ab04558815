/**
 * Simple Dialog
 */
import React from 'react'
import PropTypes from 'prop-types'

import {
  Button,
  Avatar,
  List,
  ListItem,
  ListItemAvatar,
  ListItemText,
  Dialog,
  DialogTitle,
  Typography,
  TextField,
  FormGroup,
  InputAdornment,
  DialogContent,
  IconButton,
} from '@material-ui/core'

import { withStyles } from '@material-ui/core/styles'
import { PersonIcon, AddIcon } from '@material-ui/icons'

import blue from '@material-ui/core/colors/blue'

// lang strings
import { langMessages } from '../../../lang'
import Scrollbars from 'react-custom-scrollbars'

const styles = {
  avatar: {
    // backgroundColor: blue[100],
    // color: blue[600],
  },
  dialog: {
    minWidth: 320,
    maxnWidth: '90vw',
    padding: 5,
  },
}

class SelectListDialog extends React.Component {
  state = {
    filterValue: '',
  }

  setFilterValue = filterValue => {
    this.setState({ filterValue })
  }

  handleClose = () => {
    this.props.onClose()
  }

  handleListItemClick = value => {
    this.props.onChange(value)
  }

  render() {
    const { filterValue } = this.state
    const { choices, classes, selectedValue, ...other } = this.props

    return (
      <Dialog
        onClose={this.handleClose}
        aria-labelledby="simple-dialog-title"
        open={other.open || false}
        // {...other}
      >
        <DialogTitle id="simple-dialog-title">
          <IconButton style={{ position: 'absolute', top: 5, right: 5 }} onClick={e => this.handleClose()}>
            <i className="fa fa-close font-sm"></i>
          </IconButton>
          {this.props.dialogTitle || langMessages['button.select']}
          {other.noFilter !== true && (
            <FormGroup onClick={e => e.preventDefault()}>
              <TextField
                autoFocus
                fullWidth
                value={filterValue}
                onChange={e => this.setFilterValue(e.target.value)}
                InputProps={{
                  endAdornment: (
                    <InputAdornment position="end" className="ml-5">
                      <i className="icon-magnifier"></i>
                    </InputAdornment>
                  ),
                }}
              />
            </FormGroup>
          )}
        </DialogTitle>
        <DialogContent className={classes.dialog} style={{ width: this.props.width || '100%' }}>
          <Scrollbars className="rct-scroll" autoHeight autoHeightMin={100} autoHeightMax={300} autoHide>
            <List>
              {choices
                .filter(c => !filterValue || c.label.toLowerCase().indexOf(filterValue.toLowerCase()) >= 0)
                .map((choice, k) => (
                  <ListItem button onClick={() => this.handleListItemClick(choice.value)} key={k}>
                    {(!!choice.avatar && choice.avatar) ||
                      (this.props.withAvatar && (
                        <ListItemAvatar>
                          <Avatar className={classes.avatar}>{choice.icon || (choice.label && choice.label.charAt(0)) || <PersonIcon />}</Avatar>
                        </ListItemAvatar>
                      ))}
                    <ListItemText primary={choice.label} />
                  </ListItem>
                ))}
              {other.addINewtem && (
                <ListItem
                  button
                  onClick={() => {
                    this.handleListItemClick('')
                    other.addINewtem()
                  }}
                >
                  <ListItemAvatar>
                    <Avatar>
                      <AddIcon />
                    </Avatar>
                  </ListItemAvatar>
                  <ListItemText primary={other.newtemText || langMessages['button.add']} />
                </ListItem>
              )}
            </List>
          </Scrollbars>
        </DialogContent>
      </Dialog>
    )
  }
}

SelectListDialog.propTypes = {
  classes: PropTypes.object.isRequired,
  onClose: PropTypes.func,
  selectedValue: PropTypes.string,
}

const SelectListDialogWrapped = withStyles(styles)(SelectListDialog)

class SelectListDialogComponent extends React.Component {
  state = {
    open: false,
    selectedValue: this.props.value || '',
  }

  componentDidMount() {
    this.setState({ selectedValue: this.props.value || '' })
  }

  handleClickOpen = () => {
    this.setState({ open: true })
    this.props.onClick && this.props.onClick()
  }

  handleClose = () => {
    this.setState({ open: false })
    this.props.onClose && this.props.onClose()
  }

  handleChange = value => {
    this.setState({ selectedValue: value, open: false })
    this.props.onChange(value)
  }

  render() {
    const buttonLabel = this.props.buttonLabel || langMessages['button.select']
    const { buttonColor, buttonTextColor } = this.props

    return (
      <div>
        {!!this.props.button && this.props.button}
        {this.props.button !== false && (
          <Button
            variant={this.props.variant || 'contained'}
            onClick={this.handleClickOpen}
            className={`btn-block ${!!buttonColor && 'btn-' + buttonColor} ${!!buttonTextColor && 'text-' + buttonTextColor}`}
          >
            {buttonLabel}
          </Button>
        )}
        <SelectListDialogWrapped
          {...this.props}
          choices={this.props.choices}
          selectedValue={this.state.selectedValue}
          open={this.state.open || this.props.isOpen}
          onClose={this.handleClose}
          onChange={this.handleChange}
        />
      </div>
    )
  }
}

export default SelectListDialogComponent

/* 
// props

value={""}
noFilter={false}
withAvatar
onChange={val=>{}}
onClick=={e=>{}}
onClose=={e=>{}}
button={ <Button />}
buttonLabel=""
variant="outlined"
buttonColor="success"
buttonTextColor="white"
dialogTitle=""
choices={
   [
      {
         value: "",
         label: "",
         avatar: <Avatar />,
         icon: <Icon />,
      }
   ]
}
 */
