# Environment Variables - WhatsApp Groups API

Este documento descreve as variáveis de ambiente necessárias para a API de grupos do WhatsApp (QIP-526).

## 🔧 Variáveis de Configuração

### Evolution API (Obrigatórias)

```bash
# URL base da Evolution API
WHATSAPP_API_URL=https://your-evolution-api.com

# Token de autenticação da Evolution API
WHATSAPP_API_TOKEN=your-api-token-here
```

### WhatsApp Groups (Opcionais)

```bash
# Forçar uso do mock service (apenas para testes)
# Valores: true | false
# Padrão: false (usa Evolution API real)
USE_WHATSAPP_MOCK=false

# Delay entre criações de grupos (em milissegundos)
# Padrão: 2000 (2 segundos)
GROUP_CREATION_DELAY=2000

# Máximo de leads por grupo
# Padrão: 500 (limitação do WhatsApp)
MAX_LEADS_PER_GROUP=500

# Máximo total de leads processados
# Padrão: 5000
MAX_TOTAL_LEADS=5000
```

### Ambiente

```bash
# Ambiente da aplicação
# Valores: development | production
NODE_ENV=development
```

## 🏢 Configuração por Ambiente

### Development (Homologação)
```bash
NODE_ENV=development
WHATSAPP_API_URL=https://evolution-homolog.yourcompany.com
WHATSAPP_API_TOKEN=homolog-token
USE_WHATSAPP_MOCK=false  # Usa API real de homologação
```

### Production
```bash
NODE_ENV=production
WHATSAPP_API_URL=https://evolution-prod.yourcompany.com
WHATSAPP_API_TOKEN=production-token
USE_WHATSAPP_MOCK=false  # Sempre usa API real
```

### Testing (com Mock)
```bash
NODE_ENV=development
USE_WHATSAPP_MOCK=true  # Força uso do mock
# WHATSAPP_API_URL não é necessário quando usando mock
```

## 🔍 Como a API Decide Qual Serviço Usar

A API segue esta lógica de decisão:

1. **Se `USE_WHATSAPP_MOCK=true`** → Usa mock service
2. **Se `WHATSAPP_API_URL` está configurado** → Usa Evolution API real
3. **Se `WHATSAPP_API_URL` não está configurado** → Usa mock service (fallback)

### Exemplos de Cenários

#### ✅ Cenário 1: Homologação (Recomendado)
```bash
NODE_ENV=development
WHATSAPP_API_URL=https://evolution-homolog.com
WHATSAPP_API_TOKEN=homolog-token
# USE_WHATSAPP_MOCK não definido (padrão: false)
```
**Resultado**: Usa Evolution API de homologação

#### ✅ Cenário 2: Produção
```bash
NODE_ENV=production
WHATSAPP_API_URL=https://evolution-prod.com
WHATSAPP_API_TOKEN=prod-token
USE_WHATSAPP_MOCK=false
```
**Resultado**: Usa Evolution API de produção

#### 🧪 Cenário 3: Testes com Mock
```bash
NODE_ENV=development
USE_WHATSAPP_MOCK=true
# Outras variáveis não são necessárias
```
**Resultado**: Usa mock service para testes

#### ⚠️ Cenário 4: Fallback para Mock
```bash
NODE_ENV=development
# WHATSAPP_API_URL não definido
# USE_WHATSAPP_MOCK não definido
```
**Resultado**: Usa mock service (fallback automático)

## 📊 Logs de Configuração

A API gera logs informativos sobre qual serviço está sendo usado:

### Evolution API Real
```
🗝️ Group creation response: { groupId: "...", groupJid: "..." }
```

### Mock Service
```
🔧 Using mock group service (reason: explicitly_enabled)
🔧 Using mock group service (reason: no_api_url)
```

## 🛡️ Validação de Configuração

A API valida automaticamente as configurações:

```javascript
const config = getGroupsConfig()

console.log('Configuration:', {
  useMockService: config.useMockService,
  hasRealAPI: config.hasRealAPI,
  environment: config.environment,
  mockReason: config.mockReason
})
```

### Possíveis Valores de `mockReason`:
- `explicitly_enabled` - Mock forçado via `USE_WHATSAPP_MOCK=true`
- `no_api_url` - Mock usado porque `WHATSAPP_API_URL` não está configurado
- `disabled` - Mock desabilitado, usando Evolution API real

## 🔧 Configuração Recomendada

### Para Desenvolvimento/Homologação:
```bash
# .env.development
NODE_ENV=development
WHATSAPP_API_URL=https://evolution-homolog.yourcompany.com
WHATSAPP_API_TOKEN=your-homolog-token
USE_WHATSAPP_MOCK=false
GROUP_CREATION_DELAY=1000  # Mais rápido para testes
```

### Para Produção:
```bash
# .env.production
NODE_ENV=production
WHATSAPP_API_URL=https://evolution-prod.yourcompany.com
WHATSAPP_API_TOKEN=your-production-token
USE_WHATSAPP_MOCK=false
GROUP_CREATION_DELAY=2000  # Delay padrão para evitar rate limiting
MAX_LEADS_PER_GROUP=500
MAX_TOTAL_LEADS=5000
```

### Para Testes Unitários:
```bash
# .env.test
NODE_ENV=test
USE_WHATSAPP_MOCK=true
GROUP_CREATION_DELAY=100  # Muito rápido para testes
```

## 📝 Notas Importantes

- ✅ **Mock NÃO ativa automaticamente** em development
- 🏢 **Development usa API real** (ambiente de homologação)
- 🔧 **Mock apenas quando explicitamente habilitado** via `USE_WHATSAPP_MOCK=true`
- 🛡️ **Validação automática** de configurações na inicialização
- 📊 **Logs detalhados** para debugging de configuração
- ⚡ **Fallback inteligente** para mock quando API não está disponível

## 🚀 Verificação Rápida

Para verificar se a configuração está correta, execute:

```bash
# Verificar variáveis de ambiente
echo "WHATSAPP_API_URL: $WHATSAPP_API_URL"
echo "USE_WHATSAPP_MOCK: $USE_WHATSAPP_MOCK"
echo "NODE_ENV: $NODE_ENV"

# Ou use o script de teste
node test-groups-api.js
```

O script mostrará qual serviço está sendo usado e o motivo.
