/**
 * Inset List
 */
import React from 'react'

import { Tabs, Tab } from '@material-ui/core'

const SimpleTabs = ({ tabs, activeTab, onChange, ...props }) => {
  const defaultTab = Object.keys(tabs)[0]
  let [currentTab, handleTabsState] = React.useState(activeTab || defaultTab)
  React.useEffect(() => handleTabsState(activeTab), [activeTab])

  const handleTabs = tab => {
    handleTabsState(tab)
    onChange(tab)
  }

  return (
    <Tabs indicatorColor="primary" color="default" value={tabs[currentTab] ? currentTab : defaultTab} onChange={(e, value) => handleTabs(value)}>
      {Object.keys(tabs).map((key, index) => (
        <Tab key={index} value={key} label={tabs[key]} />
      ))}
    </Tabs>
  )
}

export default SimpleTabs
