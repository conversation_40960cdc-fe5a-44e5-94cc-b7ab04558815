/**
 * DualListBox
 */
import React, { Component } from 'react'
import { connect } from 'react-redux'

// Actions
import { getPosts } from '../../../actions/PostsActions'

// lang strings
import { langMessages } from '../../../lang'

import { Scrollbars } from 'react-custom-scrollbars'

import { List, ListItem, ListItemText, Avatar, Divider, InputAdornment, TextField } from '@material-ui/core'

import { <PERSON><PERSON>, Badge } from 'reactstrap'
import SweetAlert from 'react-bootstrap-sweetalert'
import { generateKeywords, areEqualObjects, paginateArray } from '../../../helpers/helpers'
import { LinearProgress } from '@material-ui/core'
import CONSTANTS from '../../../constants/AppConstants'

const handleFocus = e => e.target.select()

let queryTimeout

class DualListBox extends Component {
  onMountTimeout = null

  state = {
    fetchedKeywords: [],
    filterText: {
      choices: this.props.filterText,
      values: '',
    },
    typing: {
      choices: false,
      values: false,
    },
    limit: false,
    choices: this.props.choices || [],
    values: this.props.values || [],
    scrollPages: {
      choices: 1,
      values: 1,
    },
    scrollHeights: {
      choices: 0,
      values: 0,
    },
  }

  componentDidMount() {
    this.setState({ choices: this.props.choices || [], values: this.props.values || [] })
    this.onMount()
  }

  componentDidUpdate(prevProps) {
    if (
      JSON.stringify(this.state.values) !== JSON.stringify(this.props.values) ||
      !areEqualObjects(prevProps.choices, this.props.choices) ||
      prevProps.collection !== this.props.collection
    ) {
      // console.log('componentDidUpdate > props',this.props.values);
      // console.log('componentDidUpdate > state',this.state.values);
      this.setState({ choices: this.props.choices || [], values: this.props.values || [] })
      clearTimeout(this.onMountTimeout)
      this.onMountTimeout = setTimeout(() => this.onMount(), 300)
    }
  }

  onMount() {
    const { choices, values, filterText } = this.state
    const { collection, posts } = this.props

    if (!collection) return

    this.handlePosts(posts)

    let limit = 20

    if (values.length) {
      let pendingIds = [...new Set(values)].filter((id, k) => !posts[collection].find(p => p.ID === id))
      let fetchIds = pendingIds.filter((id, k) => k < limit)
      let pages = paginateArray(fetchIds)

      let promises = pages.map(page => this.props.getPosts(this.props.accountId, collection, [['ID', 'in', page]]))

      Promise.all(promises).then(results => this.handlePosts(this.props.posts))

      limit = Math.max(0, limit - fetchIds.length)
    }

    if (limit && !this.props.choices.filter(c => !filterText.choices || this.filterChoice(c, filterText.choices)).length) {
      let queries = []
      let lastPos = this.state.lastPos || ''
      let orderBy = CONSTANTS.UPDATED_FIELD
      let orderArgs = { orderBy, order: 'desc' }

      this.props.getPosts(this.props.accountId, collection, queries, limit, lastPos, orderArgs).then(posts => {
        if (posts.length) {
          lastPos = window.jsonClone(posts).pop()[orderBy]
          this.setState({ lastPos })
        }
        this.handlePosts(this.props.posts)
      })
    }
  }

  handlePosts(posts) {
    let { choices } = this.state
    const { collection, createChoices } = this.props

    if (posts[collection] && posts[collection].length) {
      let collectionPosts = posts[collection]

      // console.log('collectionPosts', collectionPosts)
      if (this.props.filterPosts) {
        collectionPosts = this.props.filterPosts(collectionPosts)
      }

      if (typeof createChoices === 'function') {
        choices = createChoices(collectionPosts, choices)
      } else {
        collectionPosts.forEach(post => {
          const postId = post.ID.toString()
          let { title, content } = post
          if (!choices.filter(p => p.collection === collection && p.id === postId).length)
            choices.push({
              collection,
              id: postId.toString(),
              img: post.thumbnail || '',
              avatarColor: 'bg-primary',
              icon: '',
              title: post.title,
              subtitle: '',
              keywords: [
                ...new Set((post.keywords || []).concat(generateKeywords({ title, content }, 1))),
              ],
            })
        })
      }

      this.setState({ choices })
    }
  }

  handleAdd(id, values, maxPosts) {
    if (maxPosts && !isNaN(maxPosts) && values.length >= parseInt(maxPosts, 10)) {
      return values
    }
    values.indexOf(id) === -1 && values.push(id)
    return values
  }

  handleRemove(id, values) {
    this.onDismiss('limit')
    const i = values.indexOf(id)
    i >= 0 && values.splice(i, 1)
    return values
  }

  onChange(values) {
    this.setState({ values })
    this.props.onChange && this.props.onChange(values)
  }

  onDismiss(key) {
    this.setState({ [key]: false })
  }

  openAlert(key) {
    this.setState({ [key]: true })
  }

  onCancel(key) {
    this.setState({ [key]: false })
  }

  filterChoice(choice, searchTerm) {
    searchTerm = (searchTerm || '').toLowerCase()
    return (
      (choice.title || '').toLowerCase().indexOf(searchTerm) >= 0 ||
      (choice.subtitle || '').toLowerCase().indexOf(searchTerm) >= 0 ||
      (choice.keywords || []).find(k => k.indexOf(searchTerm) >= 0)
    )
    // || (choice.keywords||[]).find(k=>searchTerm.split(" ").find(s=>k.indexOf(s)>=0))
  }

  sortChoices(choices, searchTerm) {
    searchTerm = (searchTerm || '').toLowerCase()
    choices.sort((a, b) => {
      // no searchTerm
      if (!searchTerm) return 0

      let points = { a: 0, b: 0 }
      if ((a.title || '').toLowerCase().indexOf(searchTerm) < (b.title || '').toLowerCase().indexOf(searchTerm)) points[a] += 6
      if ((a.subtitle || '').toLowerCase().indexOf(searchTerm) < (b.subtitle || '').toLowerCase().indexOf(searchTerm)) points[a] += 5
      if ((a.keywords || []).findIndex(k => k.indexOf(searchTerm) >= 0) < (b.keywords || []).findIndex(k => k.indexOf(searchTerm) >= 0))
        points[a] += 4
      if (
        (a.keywords || []).findIndex(k => searchTerm.split(' ').find(s => k.indexOf(s) >= 0)) <
        (b.keywords || []).findIndex(k => searchTerm.split(' ').find(s => k.indexOf(s) >= 0))
      )
        points[a] += 3
      if (a.title < b.title) points[a] += 2
      if (a.subtitle < b.subtitle) points[a] += 1

      if ((a.title || '').toLowerCase().indexOf(searchTerm) > (b.title || '').toLowerCase().indexOf(searchTerm)) points[b] += 6
      if ((a.subtitle || '').toLowerCase().indexOf(searchTerm) > (b.subtitle || '').toLowerCase().indexOf(searchTerm)) points[b] += 5
      if ((a.keywords || []).findIndex(k => k.indexOf(searchTerm) >= 0) > (b.keywords || []).findIndex(k => k.indexOf(searchTerm) >= 0))
        points[b] += 4
      if (
        (a.keywords || []).findIndex(k => searchTerm.split(' ').find(s => k.indexOf(s) >= 0)) >
        (b.keywords || []).findIndex(k => searchTerm.split(' ').find(s => k.indexOf(s) >= 0))
      )
        points[b] += 3
      if (a.title > b.title) points[b] += 2
      if (a.subtitle > b.subtitle) points[b] += 1

      return points[a] === points[b] ? 0 : points[a] > points[b] ? 1 : -1
    })
    return choices
  }

  onChangeFilter(value, key) {
    const { choices, values, fetchedKeywords, typing } = this.state
    const { collection, searchKeys } = this.props

    this.setState({ filterText: { ...this.state.filterText, [key]: value }, typing: { ...typing, [key]: !!value } })
    this.props.onChangeFilter && this.props.onChangeFilter(value)
    this.resetScrollFrames()

    if (key === 'choices' || values.length > choices.length) {
      let searchTerm = value

      if (
        searchTerm &&
        searchTerm.length >= 3 &&
        !fetchedKeywords.includes(searchTerm) &&
        window.jsonClone(choices).filter(c => this.filterChoice(c, searchTerm)).length < 10
      ) {
        clearTimeout(queryTimeout)
        queryTimeout = setTimeout(
          searchTerm => {
            fetchedKeywords.push(searchTerm)
            this.setState({ fetchedKeywords, typing: { ...typing, [key]: false } })

            let queries = []
            if (searchKeys) {
              queries = searchKeys.map(s => [s.key, s.operator || '==', (s.transform && s.transform(searchTerm)) || searchTerm])
            } else {
              queries.push(['keywords', 'array-contains-any', searchTerm.split(' ').filter((k, n) => n < 10)])
            }

            let limit = 100
            // let lastPos = this.state.lastPos||'';
            // let orderBy = 'updatedAt';
            // let orderArgs = { orderBy, order: 'desc' };

            // console.log('lastPos',lastPos);
            // console.log('queries',queries);
            // console.log('fetchedKeywords',fetchedKeywords);

            this.props.getPosts(this.props.accountId, collection, queries, limit).then(posts => {
              // if ( posts.length ) {
              //     lastPos = window.jsonClone(posts).pop()[orderBy]
              //     this.setState({ lastPos })
              // }
              // console.log('lastPos',lastPos);
              // console.log('posts',posts);
              this.handlePosts(this.props.posts)
            })
          },
          500,
          searchTerm
        )
      } else {
        this.setState({ typing: { ...typing, [key]: false } })
      }
    } else {
      this.setState({ typing: { ...typing, [key]: false } })
    }
  }

  resetScrollFrames() {
    const { scrollPages, scrollHeights } = this.state

    Object.keys(scrollPages).forEach((key, i) => {
      scrollPages[key] = 1
    })

    Object.keys(scrollHeights).forEach((key, i) => {
      scrollHeights[key] = 0
    })

    this.setState({ scrollPages, scrollHeights })
  }

  handleScrollFrame(scrollData, scrollParent) {
    const { scrollPages, scrollHeights } = this.state
    const { clientHeight, scrollHeight, scrollTop } = scrollData

    let page = scrollPages[scrollParent] || 1
    let scrollTimeout

    if (clientHeight + scrollTop >= scrollHeight - 10 && scrollHeight > scrollHeights[scrollParent]) {
      clearTimeout(scrollTimeout)
      scrollTimeout = setTimeout(() => {
        page++
        scrollPages[scrollParent] = page
        scrollHeights[scrollParent] = scrollHeight
        this.setState({ scrollPages, scrollHeights })
      }, 500)
    }
  }

  render() {
    const { choices, limit, filterText, values, scrollPages, typing } = this.state
    const { maxPosts, filterInput, alertType, autoHeightMin, autoHeightMax, fetchingPost, fetchingPosts, collection } = this.props

    const filteredChoices = this.sortChoices(choices, filterText.choices).filter(c => !filterText.choices || this.filterChoice(c, filterText.choices))
    const filteredValues =
      (values &&
        values.length &&
        values.filter(id => {
          const choice = choices.find(c => c.id === id)
          return choice && (!filterText.values || this.filterChoice(choice, filterText.values))
        })) ||
      []

    let loading = {
      choices: (filterText.choices && !!fetchingPosts[collection]) || typing.choices,
      values: (filterText.values && !!fetchingPosts[collection]) || typing.values,
    }

    return (
      <div className="d-flex flex-wrap">
        <div className="flex-1 duallistbox-ul duallistbox-choices">
          {limit && alertType === 'dialog' && (
            <SweetAlert show={limit} title="Ooops" onConfirm={() => this.onDismiss('limit')} btnSize="sm" type="warning">
              {langMessages['alerts.itemsLimitReached']}
            </SweetAlert>
          )}

          {(filterInput && (
            <div className="DualListBox-filter pl-15">
              <TextField
                fullWidth
                placeholder={langMessages['texts.filterChoices']}
                value={filterText.choices || ''}
                onFocus={handleFocus}
                onChange={e => this.onChangeFilter(e.target.value, 'choices')}
                InputProps={{
                  endAdornment: (
                    <InputAdornment position="end" className="mr-5">
                      {(loading.choices && filterText.choices && (
                        <i className="fa fa-spinner fa-spin" style={{ cursor: 'pointer' }} onClick={e => this.onChangeFilter('', 'choices')}></i>
                      )) ||
                        (filterText.choices && (
                          <i className="icon-close" style={{ cursor: 'pointer' }} onClick={e => this.onChangeFilter('', 'choices')}></i>
                        )) || <i className="icon-magnifier"></i>}
                    </InputAdornment>
                  ),
                }}
              />
            </div>
          )) ||
            null}
          <div className="pb-5 pl-15" style={{ visibility: loading.choices ? 'visible' : 'hidden' }}>
            <LinearProgress style={{ height: 2, marginTop: -1 }} />
          </div>
          <Scrollbars
            className="rct-scroll"
            autoHeight
            autoHeightMin={autoHeightMin || 100}
            autoHeightMax={autoHeightMax || 350}
            onScrollFrame={e => this.handleScrollFrame(e, 'choices')}
          >
            <List className="p-0 list-divider">
              {!!filteredChoices.length &&
                filteredChoices.map((choice, key) => {
                  const page = scrollPages.choices || 1
                  const id = (choice.id && choice.id.toString()) || key

                  return (
                    (key < page * 10 && (
                      <li key={key} style={{ opacity: values && values.indexOf(id) >= 0 ? 0.5 : 1 }} {...{ 'data-id': choice.id }}>
                        <ListItem
                          button
                          onClick={e => {
                            if (maxPosts && !isNaN(maxPosts) && values.length >= parseInt(maxPosts, 10)) {
                              this.openAlert('limit')
                            } else {
                              this.onChange(this.handleAdd(id, values, maxPosts))
                            }
                          }}
                        >
                          {choice.img ? (
                            <Avatar alt={choice.title || 'Imagem'} className="img-fluid" src={choice.img} />
                          ) : (
                            <Avatar className={choice.avatarColor || ''}>
                              {choice.icon ? <i className={choice.icon}></i> : (choice.title && choice.title.charAt(0)) || 'Q'}
                            </Avatar>
                          )}
                          <ListItemText primary={choice.title || ''} secondary={choice.subtitle || ''} />
                        </ListItem>
                        {key + 1 < choices.length && <Divider variant="inset" />}
                      </li>
                    )) ||
                    null
                  )
                })}
            </List>
            {!filteredChoices.length && (
              <React.Fragment>
                {loading.choices ? (
                  <div className="alert alert-gray text-base m-15">
                    <i className="fa fa-spin fa-spinner mr-10"></i> {langMessages['texts.fetchingItems']}
                  </div>
                ) : (
                  <div className="alert alert-gray m-15">{langMessages['texts.noItemsAvailable']}</div>
                )}
              </React.Fragment>
            )}
          </Scrollbars>
        </div>
        <div className="flex-1 duallistbox-ul duallistbox-values">
          {(filterInput && (
            <div className="DualListBox-filter pl-15">
              <TextField
                fullWidth
                placeholder={langMessages['texts.filterValues']}
                value={filterText.values || ''}
                onFocus={handleFocus}
                onChange={e => this.onChangeFilter(e.target.value, 'values')}
                InputProps={{
                  endAdornment: (
                    <span className="align-items-center d-flex">
                      <InputAdornment position="end" className="mr-5">
                        {(fetchingPosts[collection] && filterText.values && (
                          <i className="fa fa-spinner fa-spin" style={{ cursor: 'pointer' }} onClick={e => this.onChangeFilter('', 'values')}></i>
                        )) ||
                          (filterText.values && (
                            <i className="icon-close" style={{ cursor: 'pointer' }} onClick={e => this.onChangeFilter('', 'values')}></i>
                          )) || <i className="icon-magnifier"></i>}
                      </InputAdornment>
                      <Badge color="dark" pill className="pl-10 pr-10">
                        {`${(values && values.length) || '0'}${(maxPosts && '/' + maxPosts) || ''}`}
                      </Badge>
                    </span>
                  ),
                }}
              />
            </div>
          )) ||
            null}
          {limit && alertType !== 'dialog' && (
            <Alert className="m-5" fade={false} color="warning" isOpen={this.state.limit} toggle={() => this.onDismiss('limit')}>
              {`${langMessages['alerts.itemsLimitReached']} (${maxPosts})`}
            </Alert>
          )}
          <div className="pb-5 pl-15" style={{ visibility: !!fetchingPosts[collection] && filterText.values ? 'visible' : 'hidden' }}>
            <LinearProgress style={{ height: 2, marginTop: -1 }} />
          </div>
          <Scrollbars
            className="rct-scroll"
            autoHeight
            autoHeightMin={autoHeightMin || 100}
            autoHeightMax={autoHeightMax || 350}
            onScrollFrame={e => this.handleScrollFrame(e, 'values')}
          >
            <List className="p-0 list-divider">
              {filteredValues.length
                ? filteredValues.map((id, key) => {
                    const page = scrollPages.values || 1
                    const choice = choices.find(c => c.id === id)
                    return (
                      (choice && choice.id && key < page * 10 && (
                        <li key={key} {...{ 'data-id': choice.id }}>
                          <ListItem
                            button
                            onClick={e => {
                              this.onChange(this.handleRemove(id, values))
                            }}
                          >
                            {choice.img ? (
                              <Avatar alt={choice.title || 'Imagem'} className="img-fluid" src={choice.img} />
                            ) : (
                              <Avatar className={choice.avatarColor || ''}>
                                {choice.icon ? <i className={choice.icon}></i> : (choice.title && choice.title.charAt(0)) || 'Q'}
                              </Avatar>
                            )}
                            <ListItemText primary={choice.title || ''} secondary={choice.subtitle || ''} />
                          </ListItem>
                          {key + 1 < values.length && <Divider variant="inset" />}
                        </li>
                      )) ||
                      null
                    )
                  })
                : null}
            </List>
            {!filteredValues.length && (
              <React.Fragment>
                {loading.values ? (
                  <div className="alert alert-gray text-base m-15">
                    <i className="fa fa-spin fa-spinner mr-10"></i> {langMessages['texts.fetchingItems']}
                  </div>
                ) : (
                  <div className="alert alert-linkedin m-15">
                    {langMessages[filterText.values ? 'components.NoItemFound' : 'texts.noItemsSelected']}
                  </div>
                )}
              </React.Fragment>
            )}
          </Scrollbars>
        </div>
      </div>
    )
  }
}

// map state to props
const mapStateToProps = ({ postsReducer, authReducer }) => {
  const { ownerId, account } = authReducer
  const { posts, fetchingPost, fetchingPosts, errorFetchingPosts } = postsReducer
  return { ownerId, account, accountId: account.ID, posts, fetchingPost, fetchingPosts, errorFetchingPosts }
}

const mapDispatchToProps = {
  getPosts,
}

export default connect(mapStateToProps, mapDispatchToProps)(DualListBox)
