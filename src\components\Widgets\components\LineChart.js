import ChartConfig from 'Constants/chart-config'
import { selectAutoColor } from 'Helpers/themeHelper'
import React from 'react'
import { Line } from 'react-chartjs-2'

// chart options
const options = {
  legend: {
    display: true,
  },
  scales: {
    xAxes: [
      {
        display: true,
      },
    ],
    yAxes: [
      {
        display: true,
        gridLines: {
          display: false,
        },
      },
    ],
  },
}

export default function LineChart(props) {
  const { darkMode, labels, campaigns } = props

  const data = canvas => {
    const colors = Object.values(ChartConfig.color)

    const datasets = campaigns.map((campaign, index) => {
      let gradientFill = selectAutoColor(index, canvas, colors, darkMode, true)
      // console.log("index", index)
      return {
        label: campaign.label,
        lineTension: 0,
        backgroundColor: gradientFill,
        borderColor: colors[index],
        borderWidth: 3,
        pointBorderWidth: 0,
        pointRadius: 0,
        data: campaign.data,
      }
    })

    return {
      labels: labels,
      datasets: datasets,
    }
  }

  return <Line data={data} options={options} height={100} />
}
